import pandas as pd
import numpy as np

def convert_to_chinese_format():
    """將數字格式的姿態數據轉換為中文格式"""
    
    print("🔄 轉換為中文格式...")
    
    # MediaPipe 關鍵點對應表
    landmark_mapping = {
        0: "鼻子",
        11: "左肩膀", 12: "右肩膀",
        13: "左手肘", 14: "右手肘", 
        15: "左手腕", 16: "右手腕",
        17: "左手小指", 18: "右手小指",
        19: "左手食指", 20: "右手食指",
        21: "左手拇指", 22: "右手拇指",
        23: "左臀部", 24: "右臀部"
    }
    
    # 讀取數據
    df = pd.read_csv('pose_data_含時間_已標記.csv')
    print(f"📊 讀取數據: {df.shape}")
    
    # 創建新的 DataFrame
    new_df = pd.DataFrame()
    
    # 複製基本欄位
    new_df['frame'] = df['frame']
    new_df['時間_秒'] = df['時間_秒']
    new_df['時間_分秒'] = df['時間_分秒']
    new_df['label'] = df['label']
    
    # 轉換關鍵點數據
    for landmark_id, chinese_name in landmark_mapping.items():
        x_col = f'{landmark_id}_x'
        y_col = f'{landmark_id}_y'
        z_col = f'{landmark_id}_z'
        vis_col = f'{landmark_id}_visibility'
        
        if x_col in df.columns:
            new_df[f'{chinese_name}_X座標'] = df[x_col]
            new_df[f'{chinese_name}_Y座標'] = df[y_col]
            new_df[f'{chinese_name}_Z座標'] = df[z_col]
            new_df[f'{chinese_name}_可見度'] = df[vis_col]
        else:
            # 如果數據不存在，填入0
            new_df[f'{chinese_name}_X座標'] = 0.0
            new_df[f'{chinese_name}_Y座標'] = 0.0
            new_df[f'{chinese_name}_Z座標'] = 0.0
            new_df[f'{chinese_name}_可見度'] = 0.0
    
    # 保存轉換後的數據
    output_file = 'pose_data_含時間_已標記_中文.csv'
    new_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✅ 轉換完成，保存為: {output_file}")
    print(f"📊 輸出數據: {new_df.shape}")
    
    # 顯示欄位名稱
    print("\n📋 轉換後的欄位:")
    for col in new_df.columns:
        if '座標' in col or '可見度' in col:
            print(f"  {col}")
    
    return new_df

def main():
    """主函數"""
    print("=" * 60)
    print("🔄 轉換姿態數據為中文格式")
    print("=" * 60)
    
    try:
        convert_to_chinese_format()
        
        print("\n" + "=" * 60)
        print("✅ 轉換完成！")
        print("📁 生成檔案: pose_data_含時間_已標記_中文.csv")
        print("\n🎯 接下來可以運行:")
        print("  python tools/csv_change.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 轉換過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
