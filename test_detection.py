"""
測試智能作業員工偵測系統
用於驗證多人環境下的主要作業員識別功能
"""

import cv2
from smart_worker_detector import SmartWorkerDetector
from config import COMMON_TASK_AREAS

def test_with_predefined_area():
    """測試使用預定義作業區域"""
    print("🧪 測試模式：使用預定義作業區域")
    
    # 使用室內機區域作為範例
    detector = SmartWorkerDetector(task_area=COMMON_TASK_AREAS['indoor_unit'])
    detector.run()

def test_manual_area_setting():
    """測試手動設定作業區域"""
    print("🧪 測試模式：手動設定作業區域")
    
    detector = SmartWorkerDetector()
    detector.run()

def test_different_areas():
    """測試不同的預設作業區域"""
    print("🧪 測試模式：比較不同作業區域效果")
    
    areas = {
        '室內機區域': COMMON_TASK_AREAS['indoor_unit'],
        '室外機區域': COMMON_TASK_AREAS['outdoor_unit'],
        '控制面板區域': COMMON_TASK_AREAS['control_panel'],
        '全畫面': COMMON_TASK_AREAS['full_frame']
    }
    
    print("可用的預設區域：")
    for i, (name, area) in enumerate(areas.items(), 1):
        print(f"  {i}. {name}: {area}")
    
    choice = input("請選擇要測試的區域 (1-4): ")
    
    try:
        choice_idx = int(choice) - 1
        area_name = list(areas.keys())[choice_idx]
        area_coords = list(areas.values())[choice_idx]
        
        print(f"✅ 選擇了 {area_name}")
        detector = SmartWorkerDetector(task_area=area_coords)
        detector.run()
        
    except (ValueError, IndexError):
        print("❌ 無效選擇，使用手動設定模式")
        test_manual_area_setting()

def quick_validation():
    """快速驗證系統是否正常運作"""
    print("🚀 快速驗證模式")
    
    try:
        # 檢查影片是否存在
        cap = cv2.VideoCapture('video_sample.mp4')
        if not cap.isOpened():
            print("❌ 找不到 video_sample.mp4，請確認影片檔案存在")
            return False
        
        ret, frame = cap.read()
        if not ret:
            print("❌ 無法讀取影片內容")
            cap.release()
            return False
        
        print(f"✅ 影片檔案正常，解析度: {frame.shape[1]}x{frame.shape[0]}")
        cap.release()
        
        # 測試 YOLO 模型
        from ultralytics import YOLO
        model = YOLO('yolov8n.pt')
        results = model.predict(frame, conf=0.5, verbose=False)
        
        people_count = 0
        if len(results) > 0 and results[0].boxes is not None:
            for box in results[0].boxes:
                if int(box.cls[0]) == 0:  # person class
                    people_count += 1
        
        print(f"✅ YOLO 偵測正常，在第一幀中發現 {people_count} 個人")
        
        # 測試 MediaPipe
        import mediapipe as mp
        mp_pose = mp.solutions.pose
        pose = mp_pose.Pose()
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results_pose = pose.process(rgb)
        
        if results_pose.pose_landmarks:
            print("✅ MediaPipe 姿勢偵測正常")
        else:
            print("⚠️ MediaPipe 在第一幀中未偵測到姿勢")
        
        pose.close()
        
        print("🎉 系統驗證完成，可以開始使用！")
        return True
        
    except Exception as e:
        print(f"❌ 驗證過程中發生錯誤: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 智能作業員工偵測系統 - 測試工具")
    print("=" * 60)
    
    print("請選擇測試模式：")
    print("1. 快速驗證系統")
    print("2. 手動設定作業區域")
    print("3. 使用預定義作業區域")
    print("4. 比較不同作業區域效果")
    print("5. 直接運行主程式")
    
    choice = input("請輸入選擇 (1-5): ").strip()
    
    if choice == '1':
        if quick_validation():
            run_main = input("是否要運行主程式？(y/n): ").strip().lower()
            if run_main == 'y':
                test_manual_area_setting()
    elif choice == '2':
        test_manual_area_setting()
    elif choice == '3':
        test_with_predefined_area()
    elif choice == '4':
        test_different_areas()
    elif choice == '5':
        from smart_worker_detector import main
        main()
    else:
        print("❌ 無效選擇")

if __name__ == "__main__":
    main()
