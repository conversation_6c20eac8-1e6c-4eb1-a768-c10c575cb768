
# PyTorch LSTM 模型摘要

## 📊 模型基本資訊
- **模型類型**: LSTM (長短期記憶網路)
- **框架**: PyTorch
- **訓練時間**: 5.7 秒
- **使用GPU**: NVIDIA GeForce GTX 1660 Ti
- **最終準確率**: 68.75%

## 🏷️ 標籤資訊
- **標籤總數**: 17 個
- **標籤列表**:
   1. 待機
   2. 旋轉工件(順時針)
   3. 結束動作
   4. 鎖第10顆螺絲
   5. 鎖第11顆螺絲
   6. 鎖第13顆螺絲
   7. 鎖第14顆螺絲
   8. 鎖第15顆螺絲
   9. 鎖第1顆螺絲
  10. 鎖第2顆螺絲
  11. 鎖第3顆螺絲
  12. 鎖第4顆螺絲
  13. 鎖第5顆螺絲
  14. 鎖第6顆螺絲
  15. 鎖第7顆螺絲
  16. 鎖第8顆螺絲
  17. 鎖第9顆螺絲

## 📈 資料統計
- **原始資料**: 1315 行
- **特徵數量**: 32 個
- **序列長度**: 10 幀
- **訓練序列**: 189 個
- **測試序列**: 48 個

## 🧠 模型架構
- **輸入層**: 32 個特徵
- **LSTM層**: 128 隱藏單元，2層
- **全連接層**: 64 → 17 (輸出)
- **總參數**: 224,401 個

## � 檔案說明
1. **sop_pytorch_model.pth**: 完整模型檔案
2. **best_sop_pytorch_model.pth**: 最佳模型權重
3. **label_encoder_pytorch.pkl**: 標籤編碼器
4. **feature_scaler_pytorch.pkl**: 特徵縮放器
5. **混淆矩陣_中文.png**: 模型預測效果圖
6. **訓練歷史_中文.png**: 訓練過程圖

## 🎯 使用方法
```python
import torch
import joblib

# 載入模型
model = torch.load('sop_pytorch_model.pth')
label_encoder = joblib.load('label_encoder_pytorch.pkl')
scaler = joblib.load('feature_scaler_pytorch.pkl')

# 進行預測
# predictions = model(input_data)
```

生成時間: 2025-07-11 11:36:46
