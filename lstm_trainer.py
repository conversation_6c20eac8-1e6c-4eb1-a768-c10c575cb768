
import numpy as np
import pandas as pd
from sklearn.preprocessing import LabelEncoder
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Masking
from tensorflow.keras.utils import to_categorical
import joblib

df = pd.read_csv('pose_data.csv')
df = df[df['label'].notnull()]

grouped = df.groupby('label')
X, y = [], []
for label, group in grouped:
    poses = group.iloc[:, 2:].values
    X.append(poses)
    y.append(label)

max_len = max(len(seq) for seq in X)
X_pad = np.zeros((len(X), max_len, X[0].shape[1]))
for i, seq in enumerate(X):
    X_pad[i, :len(seq), :] = seq

le = LabelEncoder()
y_encoded = to_categorical(le.fit_transform(y))
joblib.dump(le, 'label_encoder.pkl')

model = Sequential([
    Masking(mask_value=0.0, input_shape=(max_len, X[0].shape[1])),
    LSTM(64),
    Dense(y_encoded.shape[1], activation='softmax')
])
model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
model.fit(X_pad, y_encoded, epochs=20, batch_size=4)
model.save('sop_model.h5')
print("Model saved as sop_model.h5")
