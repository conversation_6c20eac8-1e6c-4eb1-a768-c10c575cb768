# 📁 SOP 動作監控系統 - 檔案用途說明

## 🎯 **核心程式檔案 (必須保留)**

### 主要功能程式
1. **`pose_extractor.py`** ⭐⭐⭐
   - **用途**: 從影片提取姿態數據
   - **功能**: YOLO人員檢測 + MediaPipe姿態分析
   - **輸出**: CSV格式的姿態關鍵點數據
   - **狀態**: 最新版本，物件導向設計

2. **`pytorch_lstm_trainer.py`** ⭐⭐⭐
   - **用途**: 訓練 SOP 動作識別模型
   - **功能**: 特徵工程 + LSTM神經網路訓練
   - **輸出**: 訓練好的模型檔案
   - **狀態**: 最新版本，專業級訓練器

3. **`pytorch_inference.py`** ⭐⭐⭐
   - **用途**: 即時 SOP 監控推論
   - **功能**: 載入模型進行即時動作識別
   - **輸出**: 即時監控結果
   - **狀態**: 最新版本

4. **`yolo_pose_gui.py`** ⭐⭐
   - **用途**: GUI 即時監控介面
   - **功能**: 視覺化即時監控
   - **輸出**: 圖形化監控介面
   - **狀態**: 可用

5. **`inference.py`** ⭐
   - **用途**: 舊版推論系統
   - **功能**: SOP 監控
   - **狀態**: 備用版本

### 工具程式資料夾
6. **`tools/`** ⭐⭐⭐
   - **`csv_change.py`**: 姿態數據轉SOP特徵 (核心工具)
   - **`add_time_to_csv.py`**: 添加時間資訊
   - **`add_labels_to_csv.py`**: 添加動作標籤
   - **`convert_csv_to_chinese.py`**: 轉換為繁體中文
   - **`convert_features_to_chinese.py`**: 特徵名稱中文化
   - **`README.md`**: 工具使用說明
   - **`使用教學.txt`**: 詳細教學
   - **`快速執行.bat`**: 批次執行腳本

## 🏆 **模型檔案 (保留)**

### 最新模型 (主要使用)
7. **`enhanced_sop_models/`** ⭐⭐⭐
   - **`enhanced_sop_lstm_model.pth`**: 最新訓練的模型 (99.64%準確率)
   - **`config.json`**: 模型配置
   - **`scaler.pkl`**: 特徵標準化器
   - **`label_encoder.pkl`**: 標籤編碼器
   - **`detailed_report.json`**: 詳細訓練報告
   - **`model_summary.json`**: 模型摘要
   - **`feature_columns.json`**: 特徵欄位定義

### 舊模型 (備份)
8. **`pytorch_models/`** ⭐
   - **`sop_pytorch_model.pth`**: 舊版模型
   - **`best_sop_pytorch_model.pth`**: 舊版最佳模型
   - **`feature_scaler_pytorch.pkl`**: 舊版標準化器
   - **`label_encoder_pytorch.pkl`**: 舊版標籤編碼器
   - **`影片2測試結果.csv`**: 測試結果
   - **`*.png`**: 可視化圖表
   - **`模型摘要.md`**: 舊版模型說明

## 📹 **影片檔案**

### 原始影片
9. **`1.mp4`** ⭐⭐⭐
   - **用途**: 主要訓練影片
   - **內容**: 完整 SOP 動作序列
   - **狀態**: 核心數據來源

10. **`2.mp4`** ⭐
    - **用途**: 測試影片
    - **內容**: SOP 動作
    - **狀態**: 備用測試數據

### 處理後影片
11. **`1_processed.mp4`** ❌
    - **用途**: 舊版處理結果
    - **狀態**: 可刪除

12. **`1_processed_new.mp4`** ⭐
    - **用途**: 新版處理結果
    - **狀態**: 保留作為參考

## ⚙️ **配置檔案**

13. **`sop_config.json`** ⭐⭐
    - **用途**: SOP 監控配置
    - **內容**: 閾值、參數設定

14. **`task_area_config.json`** ⭐
    - **用途**: 作業區域配置
    - **內容**: 工作區域定義

15. **`yolov8n.pt`** ⭐⭐⭐
    - **用途**: YOLO 人員檢測模型
    - **狀態**: 必須保留

## 📄 **文檔檔案**

16. **`README.txt`** ⭐
    - **用途**: 專案說明
    - **狀態**: 基本說明

17. **`SOP完整分析報告_統整版.txt`** ⭐
    - **用途**: 詳細分析報告
    - **狀態**: 參考文檔

18. **`sop_report_20250711_142747.json`** ❌
    - **用途**: 臨時報告檔案
    - **狀態**: 可刪除

## ❌ **建議刪除的檔案**

### 臨時/重複檔案
- `1_processed.mp4` (舊版處理結果)
- `sop_report_20250711_142747.json` (臨時報告)

### 可選刪除 (如果空間不足)
- `pytorch_models/` 整個資料夾 (舊版模型，已有新版)
- `2.mp4` (測試影片，非必須)
- `1_processed_new.mp4` (處理結果，可重新生成)

## 🎯 **最小核心檔案集合**

如果只保留最核心的檔案：
1. `pose_extractor.py`
2. `pytorch_lstm_trainer.py` 
3. `pytorch_inference.py`
4. `tools/csv_change.py`
5. `enhanced_sop_models/` (整個資料夾)
6. `1.mp4`
7. `yolov8n.pt`
8. `sop_config.json`

## 📊 **檔案重要性評級**
- ⭐⭐⭐ 核心必須 (不可刪除)
- ⭐⭐ 重要 (建議保留)
- ⭐ 一般 (可選保留)
- ❌ 建議刪除
