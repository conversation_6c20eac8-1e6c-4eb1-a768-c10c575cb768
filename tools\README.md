# SOP姿態分析工具集

## 📁 工具概覽

本資料夾包含4個Python工具，用於處理SOP姿態分析的資料轉換：

| 工具名稱 | 功能 | 輸入 | 輸出 |
|---------|------|------|------|
| `add_time_to_csv.py` | 時間轉換 | pose_data.csv | pose_data_含時間.csv |
| `convert_csv_to_chinese.py` | 姿態資料中文化 | pose_data.csv | pose_data_繁體中文.csv |
| `csv_change.py` | SOP特徵提取 | pose_data_含時間.csv | sop_features.csv |
| `convert_features_to_chinese.py` | 特徵資料中文化 | sop_features.csv | sop_features_繁體中文.csv |

## 🚀 快速開始

### 方法1：使用批次檔案（推薦）
```bash
# 雙擊執行
tools\快速執行.bat
```

### 方法2：手動執行
```bash
# 步驟1：添加時間資訊
python tools\add_time_to_csv.py

# 步驟2：姿態資料中文化
python tools\convert_csv_to_chinese.py

# 步驟3：提取SOP特徵
python tools\csv_change.py

# 步驟4：特徵資料中文化
python tools\convert_features_to_chinese.py
```

## 📋 詳細說明

### 🔧 add_time_to_csv.py
- **功能**：將幀數轉換為時間資訊
- **新增欄位**：時間_秒、時間_分秒
- **用途**：方便進行時間軸分析

### 🔧 convert_csv_to_chinese.py
- **功能**：將姿態資料欄位名稱轉換為繁體中文
- **轉換範例**：`0_x` → `鼻子_X座標`
- **用途**：提高可讀性

### 🔧 csv_change.py
- **功能**：從姿態資料中提取SOP監控特徵
- **特徵類別**：角度、速度、距離、姿勢、穩定性、節奏、合規性
- **用途**：為LSTM模型準備訓練資料

### 🔧 convert_features_to_chinese.py
- **功能**：將特徵欄位名稱轉換為繁體中文
- **轉換範例**：`left_elbow_angle` → `左手肘角度`
- **用途**：提高分析報告的可讀性

## 📊 處理流程

```
原始影片
    ↓
[pose_extractor.py] → pose_data.csv
    ↓
[add_time_to_csv.py] → pose_data_含時間.csv
    ↓
[csv_change.py] → sop_features.csv
    ↓
[convert_features_to_chinese.py] → sop_features_繁體中文.csv
```

## ⚠️ 注意事項

1. **執行順序**：請按照上述流程順序執行
2. **檔案位置**：確保輸入檔案位於專案根目錄
3. **Python環境**：需要安裝 pandas, numpy, opencv-python
4. **編碼問題**：輸出檔案使用UTF-8編碼

## 📞 技術支援

詳細使用教學請參考：`使用教學.txt`

如有問題，請檢查：
- Python版本（建議3.7+）
- 必要套件是否已安裝
- 檔案路徑是否正確
- 輸入檔案是否存在且格式正確
