import pandas as pd
import os

def convert_pose_data_to_chinese():
    """轉換 pose_data.csv 為繁體中文"""
    print("🔄 轉換 pose_data.csv...")
    
    # MediaPipe 關鍵點對應表
    landmark_mapping = {
        0: "鼻子", 1: "左眼內角", 2: "左眼中心", 3: "左眼外角", 4: "右眼內角", 
        5: "右眼中心", 6: "右眼外角", 7: "左耳", 8: "右耳", 9: "嘴巴左角", 
        10: "嘴巴右角", 11: "左肩膀", 12: "右肩膀", 13: "左手肘", 14: "右手肘", 
        15: "左手腕", 16: "右手腕", 17: "左手小指", 18: "右手小指", 19: "左手食指", 
        20: "右手食指", 21: "左手拇指", 22: "右手拇指", 23: "左臀部", 24: "右臀部",
        25: "左膝蓋", 26: "右膝蓋", 27: "左腳踝", 28: "右腳踝", 29: "左腳跟", 
        30: "右腳跟", 31: "左腳趾", 32: "右腳趾"
    }
    
    df = pd.read_csv('pose_data.csv')
    new_df = pd.DataFrame()
    
    # 基本欄位
    new_df['幀數'] = df['frame']
    new_df['標籤'] = df['label']
    
    # 轉換關鍵點數據
    for landmark_id, chinese_name in landmark_mapping.items():
        x_col = f'{landmark_id}_x'
        y_col = f'{landmark_id}_y'
        z_col = f'{landmark_id}_z'
        vis_col = f'{landmark_id}_visibility'
        
        if x_col in df.columns:
            new_df[f'{chinese_name}_X座標'] = df[x_col]
            new_df[f'{chinese_name}_Y座標'] = df[y_col]
            new_df[f'{chinese_name}_Z座標'] = df[z_col]
            new_df[f'{chinese_name}_可見度'] = df[vis_col]
    
    new_df.to_csv('pose_data_繁體中文.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 已轉換: pose_data_繁體中文.csv ({new_df.shape})")

def convert_pose_data_with_time_to_chinese():
    """轉換 pose_data_含時間.csv 為繁體中文"""
    print("🔄 轉換 pose_data_含時間.csv...")
    
    # MediaPipe 關鍵點對應表
    landmark_mapping = {
        0: "鼻子", 1: "左眼內角", 2: "左眼中心", 3: "左眼外角", 4: "右眼內角", 
        5: "右眼中心", 6: "右眼外角", 7: "左耳", 8: "右耳", 9: "嘴巴左角", 
        10: "嘴巴右角", 11: "左肩膀", 12: "右肩膀", 13: "左手肘", 14: "右手肘", 
        15: "左手腕", 16: "右手腕", 17: "左手小指", 18: "右手小指", 19: "左手食指", 
        20: "右手食指", 21: "左手拇指", 22: "右手拇指", 23: "左臀部", 24: "右臀部",
        25: "左膝蓋", 26: "右膝蓋", 27: "左腳踝", 28: "右腳踝", 29: "左腳跟", 
        30: "右腳跟", 31: "左腳趾", 32: "右腳趾"
    }
    
    df = pd.read_csv('pose_data_含時間.csv')
    new_df = pd.DataFrame()
    
    # 基本欄位
    new_df['幀數'] = df['frame']
    new_df['時間_秒'] = df['時間_秒']
    new_df['時間_分秒'] = df['時間_分秒']
    new_df['標籤'] = df['label']
    
    # 轉換關鍵點數據
    for landmark_id, chinese_name in landmark_mapping.items():
        x_col = f'{landmark_id}_x'
        y_col = f'{landmark_id}_y'
        z_col = f'{landmark_id}_z'
        vis_col = f'{landmark_id}_visibility'
        
        if x_col in df.columns:
            new_df[f'{chinese_name}_X座標'] = df[x_col]
            new_df[f'{chinese_name}_Y座標'] = df[y_col]
            new_df[f'{chinese_name}_Z座標'] = df[z_col]
            new_df[f'{chinese_name}_可見度'] = df[vis_col]
    
    new_df.to_csv('pose_data_含時間_繁體中文.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 已轉換: pose_data_含時間_繁體中文.csv ({new_df.shape})")

def convert_pose_data_labeled_to_chinese():
    """轉換 pose_data_含時間_已標記.csv 為繁體中文"""
    print("🔄 轉換 pose_data_含時間_已標記.csv...")
    
    # MediaPipe 關鍵點對應表
    landmark_mapping = {
        0: "鼻子", 1: "左眼內角", 2: "左眼中心", 3: "左眼外角", 4: "右眼內角", 
        5: "右眼中心", 6: "右眼外角", 7: "左耳", 8: "右耳", 9: "嘴巴左角", 
        10: "嘴巴右角", 11: "左肩膀", 12: "右肩膀", 13: "左手肘", 14: "右手肘", 
        15: "左手腕", 16: "右手腕", 17: "左手小指", 18: "右手小指", 19: "左手食指", 
        20: "右手食指", 21: "左手拇指", 22: "右手拇指", 23: "左臀部", 24: "右臀部",
        25: "左膝蓋", 26: "右膝蓋", 27: "左腳踝", 28: "右腳踝", 29: "左腳跟", 
        30: "右腳跟", 31: "左腳趾", 32: "右腳趾"
    }
    
    df = pd.read_csv('pose_data_含時間_已標記.csv')
    new_df = pd.DataFrame()
    
    # 基本欄位
    new_df['幀數'] = df['frame']
    new_df['時間_秒'] = df['時間_秒']
    new_df['時間_分秒'] = df['時間_分秒']
    new_df['標籤'] = df['label']
    
    # 轉換關鍵點數據
    for landmark_id, chinese_name in landmark_mapping.items():
        x_col = f'{landmark_id}_x'
        y_col = f'{landmark_id}_y'
        z_col = f'{landmark_id}_z'
        vis_col = f'{landmark_id}_visibility'
        
        if x_col in df.columns:
            new_df[f'{chinese_name}_X座標'] = df[x_col]
            new_df[f'{chinese_name}_Y座標'] = df[y_col]
            new_df[f'{chinese_name}_Z座標'] = df[z_col]
            new_df[f'{chinese_name}_可見度'] = df[vis_col]
    
    new_df.to_csv('pose_data_含時間_已標記_繁體中文.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 已轉換: pose_data_含時間_已標記_繁體中文.csv ({new_df.shape})")

def convert_sop_features_to_chinese():
    """轉換 sop_features_已標記.csv 為繁體中文"""
    print("🔄 轉換 sop_features_已標記.csv...")
    
    # 特徵欄位對應表
    chinese_headers = {
        'frame': '幀數',
        'time': '時間_秒',
        'label': '標籤',
        'left_elbow_angle': '左手肘角度',
        'right_elbow_angle': '右手肘角度',
        'left_wrist_angle': '左手腕角度',
        'right_wrist_angle': '右手腕角度',
        'left_arm_extension': '左臂伸展程度',
        'right_arm_extension': '右臂伸展程度',
        'left_wrist_speed': '左手腕速度',
        'right_wrist_speed': '右手腕速度',
        'left_trajectory_regularity': '左手軌跡規律性',
        'right_trajectory_regularity': '右手軌跡規律性',
        'hands_distance': '雙手距離',
        'shoulder_level': '肩膀水平度',
        'body_lean': '身體前傾程度',
        'head_position': '頭部位置',
        'left_finger_spread': '左手手指張開度',
        'right_finger_spread': '右手手指張開度',
        'left_wrist_stability': '左手腕穩定性',
        'right_wrist_stability': '右手腕穩定性',
        'left_action_frequency': '左手動作頻率',
        'right_action_frequency': '右手動作頻率',
        'left_pause_time': '左手停頓時間',
        'right_pause_time': '右手停頓時間',
        'work_rhythm': '工作節奏',
        'elbow_angle_ok': '手肘角度正常',
        'wrist_rotation_ok': '手腕旋轉正常',
        'speed_ok': '動作速度正常',
        'trajectory_ok': '軌跡規律正常',
        'posture_ok': '姿勢正常',
        'head_position_ok': '頭部位置正常',
        'frequency_ok': '動作頻率正常',
        'pause_time_ok': '停頓時間正常',
        'compliance_score': 'SOP合規性評分'
    }
    
    df = pd.read_csv('sop_features_已標記.csv')
    
    # 重新命名欄位
    df_chinese = df.rename(columns=chinese_headers)
    
    df_chinese.to_csv('sop_features_已標記_繁體中文.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 已轉換: sop_features_已標記_繁體中文.csv ({df_chinese.shape})")

def convert_pose_output_to_chinese():
    """轉換 pose_output/1_pose_data.csv 為繁體中文"""
    print("🔄 轉換 pose_output/1_pose_data.csv...")
    
    df = pd.read_csv('pose_output/1_pose_data.csv')
    
    # 基本欄位對應
    chinese_headers = {
        'frame_idx': '幀索引',
        'timestamp': '時間戳',
        'person_box_x1': '人員邊界框_X1',
        'person_box_y1': '人員邊界框_Y1', 
        'person_box_x2': '人員邊界框_X2',
        'person_box_y2': '人員邊界框_Y2',
        'visibility_score': '可見度評分'
    }
    
    # MediaPipe 關鍵點對應
    landmark_mapping = {
        0: "鼻子", 1: "左眼內角", 2: "左眼中心", 3: "左眼外角", 4: "右眼內角", 
        5: "右眼中心", 6: "右眼外角", 7: "左耳", 8: "右耳", 9: "嘴巴左角", 
        10: "嘴巴右角", 11: "左肩膀", 12: "右肩膀", 13: "左手肘", 14: "右手肘", 
        15: "左手腕", 16: "右手腕", 17: "左手小指", 18: "右手小指", 19: "左手食指", 
        20: "右手食指", 21: "左手拇指", 22: "右手拇指", 23: "左臀部", 24: "右臀部",
        25: "左膝蓋", 26: "右膝蓋", 27: "左腳踝", 28: "右腳踝", 29: "左腳跟", 
        30: "右腳跟", 31: "左腳趾", 32: "右腳趾"
    }
    
    # 添加關鍵點欄位對應
    for i in range(33):
        if i in landmark_mapping:
            chinese_name = landmark_mapping[i]
            chinese_headers[f'landmark_{i}_x'] = f'{chinese_name}_X座標'
            chinese_headers[f'landmark_{i}_y'] = f'{chinese_name}_Y座標'
            chinese_headers[f'landmark_{i}_z'] = f'{chinese_name}_Z座標'
            chinese_headers[f'landmark_{i}_visibility'] = f'{chinese_name}_可見度'
    
    # 重新命名欄位
    df_chinese = df.rename(columns=chinese_headers)
    
    df_chinese.to_csv('pose_output/1_pose_data_繁體中文.csv', index=False, encoding='utf-8-sig')
    print(f"✅ 已轉換: pose_output/1_pose_data_繁體中文.csv ({df_chinese.shape})")

def main():
    """主函數"""
    print("=" * 60)
    print("🌏 轉換所有 CSV 檔案為繁體中文")
    print("=" * 60)
    
    os.chdir('video1_training_data')
    
    try:
        # 轉換所有檔案
        convert_pose_data_to_chinese()
        convert_pose_data_with_time_to_chinese()
        convert_pose_data_labeled_to_chinese()
        convert_sop_features_to_chinese()
        convert_pose_output_to_chinese()
        
        print("\n" + "=" * 60)
        print("✅ 所有 CSV 檔案已轉換為繁體中文！")
        print("📁 生成的繁體中文檔案:")
        print("  - pose_data_繁體中文.csv")
        print("  - pose_data_含時間_繁體中文.csv")
        print("  - pose_data_含時間_已標記_繁體中文.csv")
        print("  - sop_features_已標記_繁體中文.csv")
        print("  - pose_output/1_pose_data_繁體中文.csv")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 轉換過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
