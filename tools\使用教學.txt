SOP姿態分析工具使用教學
================================================================================
創建時間：2025年07月10日
版本：v1.0
================================================================================

📁 工具資料夾說明
================================================================================

本資料夾包含4個Python工具，用於處理SOP姿態分析的資料轉換：

1. add_time_to_csv.py          - 時間轉換工具
2. convert_csv_to_chinese.py   - 姿態資料中文化工具  
3. csv_change.py               - SOP特徵提取工具
4. convert_features_to_chinese.py - 特徵資料中文化工具

================================================================================
🎯 完整處理流程
================================================================================

原始影片 → 姿態檢測 → 資料轉換 → 特徵提取 → 中文化 → 分析報告

詳細步驟：
1. 使用 pose_extractor.py 從影片提取姿態資料 → pose_data.csv
2. 使用 add_time_to_csv.py 添加時間資訊 → pose_data_含時間.csv
3. 使用 convert_csv_to_chinese.py 中文化姿態資料 → pose_data_繁體中文.csv
4. 使用 csv_change.py 提取SOP特徵 → sop_features.csv
5. 使用 convert_features_to_chinese.py 中文化特徵 → sop_features_繁體中文.csv

================================================================================
📋 工具詳細說明
================================================================================

🔧 工具1：add_time_to_csv.py
────────────────────────────────────────────────────────────────────────────────

【功能】
將幀數轉換為時間資訊，方便進行時間軸分析

【輸入檔案】
• pose_data.csv 或 pose_data_繁體中文.csv

【輸出檔案】
• pose_data_含時間.csv

【新增欄位】
• 時間_秒：精確的秒數（如：0.033367）
• 時間_分秒：格式化時間（如：00:00.03）

【使用方法】
1. 確保工作目錄有 pose_data.csv 或相關影片檔案
2. 開啟命令提示字元，切換到專案目錄
3. 執行：python tools\add_time_to_csv.py

【執行結果範例】
從 1.mp4 獲取幀率: 29.97 FPS
讀取CSV檔案: pose_data_繁體中文.csv
使用幀數欄位: 幀數
轉換完成！
影片幀率: 29.97 FPS
總幀數: 1315
總時長: 52.99 秒 (00:52.98)
新檔案已保存為: pose_data_含時間.csv

────────────────────────────────────────────────────────────────────────────────

🔧 工具2：convert_csv_to_chinese.py
────────────────────────────────────────────────────────────────────────────────

【功能】
將英文的姿態資料欄位名稱轉換為繁體中文

【輸入檔案】
• pose_data.csv

【輸出檔案】
• pose_data_繁體中文.csv

【轉換範例】
• frame → 幀數
• 0_x → 鼻子_X座標
• 1_x → 左眼內角_X座標
• 0_visibility → 鼻子_可見度

【使用方法】
1. 確保工作目錄有 pose_data.csv
2. 執行：python tools\convert_csv_to_chinese.py

【執行結果範例】
正在讀取原始CSV檔案...
正在轉換欄位名稱為繁體中文...
正在保存新的CSV檔案...
轉換完成！新檔案已保存為 'pose_data_繁體中文.csv'
總共處理了 1315 行資料
前5個欄位名稱: ['幀數', '標籤', '鼻子_X座標', '鼻子_Y座標', '鼻子_Z座標']

────────────────────────────────────────────────────────────────────────────────

🔧 工具3：csv_change.py
────────────────────────────────────────────────────────────────────────────────

【功能】
從姿態資料中提取SOP監控相關特徵，進行深度分析

【輸入檔案】
• pose_data_含時間.csv

【輸出檔案】
• sop_features.csv

【提取的特徵類別】
1. 角度特徵：手肘角度、手腕角度
2. 速度特徵：手腕移動速度
3. 距離特徵：臂部伸展、雙手距離
4. 姿勢特徵：肩膀水平度、身體前傾、頭部位置
5. 穩定性特徵：軌跡規律性、動作穩定性
6. 節奏特徵：動作頻率、停頓時間
7. 合規性評分：SOP標準檢查

【使用方法】
1. 確保工作目錄有 pose_data_含時間.csv
2. 執行：python tools\csv_change.py

【執行結果範例】
正在提取SOP監控特徵...
正在分析合規性...
特徵提取完成！已保存為 'sop_features.csv'
總共提取了 1315 行資料，35 個特徵

────────────────────────────────────────────────────────────────────────────────

🔧 工具4：convert_features_to_chinese.py
────────────────────────────────────────────────────────────────────────────────

【功能】
將英文的SOP特徵欄位名稱轉換為繁體中文

【輸入檔案】
• sop_features.csv

【輸出檔案】
• sop_features_繁體中文.csv

【轉換範例】
• left_elbow_angle → 左手肘角度
• right_wrist_speed → 右手腕速度
• compliance_score → SOP合規性評分

【使用方法】
1. 確保工作目錄有 sop_features.csv
2. 執行：python tools\convert_features_to_chinese.py

【執行結果範例】
正在讀取特徵CSV檔案...
正在轉換欄位名稱為繁體中文...
正在保存新的CSV檔案...
轉換完成！新檔案已保存為 'sop_features_繁體中文.csv'
總共處理了 1315 行資料
特徵數量: 35 個

================================================================================
🚀 完整操作步驟教學
================================================================================

假設您已經有了 pose_data.csv（從 pose_extractor.py 產生），以下是完整的處理步驟：

步驟1：添加時間資訊
────────────────────────────────────────────────────────────────────────────────
命令：python tools\add_time_to_csv.py

目的：將幀數轉換為實際時間
輸入：pose_data.csv 或 pose_data_繁體中文.csv
輸出：pose_data_含時間.csv

步驟2：姿態資料中文化（可選）
────────────────────────────────────────────────────────────────────────────────
命令：python tools\convert_csv_to_chinese.py

目的：將姿態資料欄位名稱改為繁體中文
輸入：pose_data.csv
輸出：pose_data_繁體中文.csv

步驟3：提取SOP特徵
────────────────────────────────────────────────────────────────────────────────
命令：python tools\csv_change.py

目的：從姿態資料中提取SOP監控特徵
輸入：pose_data_含時間.csv
輸出：sop_features.csv

步驟4：特徵資料中文化
────────────────────────────────────────────────────────────────────────────────
命令：python tools\convert_features_to_chinese.py

目的：將特徵欄位名稱改為繁體中文
輸入：sop_features.csv
輸出：sop_features_繁體中文.csv

================================================================================
⚠️ 常見問題與解決方案
================================================================================

問題1：找不到輸入檔案
────────────────────────────────────────────────────────────────────────────────
錯誤訊息：找不到 'pose_data.csv' 檔案
解決方案：
1. 確認檔案是否存在於專案根目錄
2. 檢查檔案名稱是否正確
3. 確認是否已經執行過前面的步驟

問題2：編碼問題（中文亂碼）
────────────────────────────────────────────────────────────────────────────────
現象：CSV檔案中的中文顯示為亂碼
解決方案：
1. 使用支援UTF-8的編輯器開啟（如VS Code）
2. 在Excel中開啟時選擇UTF-8編碼
3. 重新執行轉換工具

問題3：Python模組缺失
────────────────────────────────────────────────────────────────────────────────
錯誤訊息：ModuleNotFoundError: No module named 'pandas'
解決方案：
安裝必要的Python套件：
pip install pandas numpy opencv-python

問題4：路徑問題
────────────────────────────────────────────────────────────────────────────────
錯誤訊息：系統找不到指定的路徑
解決方案：
1. 確認當前目錄是專案根目錄
2. 使用 cd 命令切換到正確目錄
3. 確認 tools 資料夾存在

================================================================================
📊 輸出檔案說明
================================================================================

pose_data_含時間.csv
────────────────────────────────────────────────────────────────────────────────
• 包含原始姿態資料 + 時間資訊
• 用途：時間軸分析、影片對照
• 欄位：幀數、時間_秒、時間_分秒、各關鍵點座標

pose_data_繁體中文.csv
────────────────────────────────────────────────────────────────────────────────
• 繁體中文版的姿態資料
• 用途：方便中文使用者理解
• 欄位：幀數、標籤、鼻子_X座標、左眼內角_X座標...

sop_features_繁體中文.csv
────────────────────────────────────────────────────────────────────────────────
• 繁體中文版的SOP特徵資料
• 用途：LSTM模型訓練、SOP合規性分析
• 欄位：幀數、時間_秒、左手肘角度、右手腕速度、SOP合規性評分...

================================================================================
🎯 下一步建議
================================================================================

完成資料轉換後，您可以：

1. 【資料分析】
   使用 sop_features_繁體中文.csv 進行統計分析

2. 【模型訓練】
   使用 lstm_trainer.py 訓練LSTM模型

3. 【即時監控】
   使用 yolo_pose_gui.py 進行即時SOP監控

4. 【報告生成】
   參考 SOP完整分析報告_統整版.txt 了解分析結果

================================================================================
📞 技術支援
================================================================================

如果遇到問題，請檢查：
1. Python版本（建議3.7+）
2. 必要套件是否已安裝
3. 檔案路徑是否正確
4. 輸入檔案是否存在且格式正確

================================================================================
