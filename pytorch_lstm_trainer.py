import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, WeightedRandomSampler
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from collections import Counter
import time
import os
import shutil
import warnings
from datetime import datetime
import json
import logging
from typing import Dict, List, Tuple, Optional
import matplotlib.font_manager as fm

# 配置警告和日誌
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 設置繁體中文字體支援
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 設置設備
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用設備: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")

class SOPDataset(Dataset):
    """增強的 SOP 資料集類別"""
    def __init__(self, sequences, labels, augment=False):
        self.sequences = torch.FloatTensor(sequences)
        self.labels = torch.LongTensor(labels)
        self.augment = augment
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]
        
        # 資料增強
        if self.augment and torch.rand(1) < 0.3:
            sequence = self.augment_sequence(sequence)
        
        return sequence, label
    
    def augment_sequence(self, sequence):
        """序列資料增強"""
        # 添加小量噪音
        noise = torch.randn_like(sequence) * 0.01
        sequence = sequence + noise
        
        # 隨機時間偏移
        if torch.rand(1) < 0.3:
            shift = torch.randint(-2, 3, (1,)).item()
            if shift != 0:
                if shift > 0:
                    sequence = torch.cat([sequence[shift:], sequence[-shift:]], dim=0)
                else:
                    sequence = torch.cat([sequence[:shift], sequence[:-shift]], dim=0)
        
        return sequence

class ImprovedSOPLSTM(nn.Module):
    """改進的 SOP LSTM 模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes, dropout=0.3):
        super(ImprovedSOPLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # 雙向 LSTM
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 注意力機制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 分類器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, num_classes)
        )
        
        # 批次正規化
        self.batch_norm = nn.BatchNorm1d(hidden_size * 2)
        
    def forward(self, x):
        # LSTM 前向傳播
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 注意力機制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 全域平均池化
        pooled = torch.mean(attn_out, dim=1)
        
        # 批次正規化
        pooled = self.batch_norm(pooled)
        
        # 分類
        output = self.classifier(pooled)
        
        return output

class EnhancedSOPTrainer:
    """增強的 SOP 訓練器"""
    def __init__(self, csv_file='sop_features_已標記_繁體中文.csv', config=None):
        self.csv_file = csv_file
        self.config = config or self.get_default_config()
        self.label_encoder = LabelEncoder()
        self.scaler = RobustScaler()  # 使用更穩健的縮放器
        self.model = None
        self.device = device
        self.training_history = {}
        self.model_metrics = {}
        
        # 創建輸出目錄
        self.output_dir = 'enhanced_sop_models'
        os.makedirs(self.output_dir, exist_ok=True)
        
    def get_default_config(self):
        """獲取默認配置"""
        return {
            'sequence_length': 15,
            'hidden_size': 128,
            'num_layers': 2,
            'dropout': 0.3,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'early_stopping_patience': 15,
            'weight_decay': 1e-4,
            'use_class_weights': True,
            'use_data_augmentation': True,
            'cross_validation_folds': 5
        }
    
    def load_and_preprocess_data(self):
        """載入和預處理資料"""
        logger.info("正在載入資料...")
        try:
            df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
            logger.info(f"✅ 成功載入資料，形狀: {df.shape}")
        except Exception as e:
            logger.error(f"❌ 載入資料失敗: {e}")
            raise

        # 資料品質檢查
        logger.info("進行資料品質檢查...")
        self.data_quality_check(df)

        # 標籤分佈分析
        label_counts = df['標籤'].value_counts()
        logger.info(f"標籤分佈: {dict(label_counts)}")

        # 處理類別不平衡
        df_balanced = self.handle_class_imbalance(df)
        
        # 特徵選擇和工程
        feature_columns = self.feature_engineering(df_balanced)
        
        return df_balanced, feature_columns
    
    def data_quality_check(self, df):
        """資料品質檢查"""
        logger.info("=" * 50)
        logger.info("資料品質報告")
        logger.info("=" * 50)
        
        # 缺失值檢查
        missing_values = df.isnull().sum()
        if missing_values.any():
            logger.warning(f"發現缺失值: {missing_values[missing_values > 0].to_dict()}")
        
        # 重複值檢查
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            logger.warning(f"發現重複行: {duplicates}")
        
        # 異常值檢查
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col not in ['幀數', '時間_秒']:
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1
                outliers = df[(df[col] < q1 - 1.5 * iqr) | (df[col] > q3 + 1.5 * iqr)]
                if len(outliers) > 0:
                    logger.warning(f"欄位 {col} 發現 {len(outliers)} 個異常值")
        
        logger.info("✅ 資料品質檢查完成")
    
    def handle_class_imbalance(self, df):
        """處理類別不平衡問題"""
        logger.info("處理類別不平衡...")
        
        label_counts = df['標籤'].value_counts()
        min_samples = label_counts.min()
        max_samples = label_counts.max()
        
        if max_samples / min_samples > 3:  # 如果不平衡比例超過 3:1
            logger.warning(f"檢測到類別不平衡: 最多 {max_samples} 樣本, 最少 {min_samples} 樣本")
            
            # 使用 SMOTE 或過採樣技術
            balanced_dfs = []
            target_samples = int(np.median(label_counts))
            
            for label in label_counts.index:
                label_df = df[df['標籤'] == label]
                current_samples = len(label_df)
                
                if current_samples < target_samples:
                    # 過採樣
                    multiplier = target_samples // current_samples
                    remainder = target_samples % current_samples
                    
                    oversampled = pd.concat([label_df] * multiplier, ignore_index=True)
                    if remainder > 0:
                        additional = label_df.sample(n=remainder, replace=True)
                        oversampled = pd.concat([oversampled, additional], ignore_index=True)
                    
                    balanced_dfs.append(oversampled)
                else:
                    balanced_dfs.append(label_df)
            
            df_balanced = pd.concat(balanced_dfs, ignore_index=True)
            logger.info(f"平衡後資料形狀: {df_balanced.shape}")
            return df_balanced
        
        return df
    
    def feature_engineering(self, df):
        """特徵工程"""
        logger.info("進行特徵工程...")
        
        # 基本特徵選擇
        exclude_columns = ['幀數', '時間_秒', '標籤', '時間_分秒']
        feature_columns = []
        
        for col in df.columns:
            if col not in exclude_columns:
                try:
                    if df[col].dtype in ['int64', 'float64', 'bool', 'int32', 'float32']:
                        df[col] = pd.to_numeric(df[col], errors='coerce').astype(np.float32)
                        feature_columns.append(col)
                    elif col.endswith('正常') or col.endswith('_flag'):
                        df[col] = df[col].astype(int).astype(np.float32)
                        feature_columns.append(col)
                    else:
                        numeric_col = pd.to_numeric(df[col], errors='coerce')
                        if not numeric_col.isna().all():
                            df[col] = numeric_col.astype(np.float32)
                            feature_columns.append(col)
                except Exception as e:
                    logger.warning(f"處理欄位 {col} 時發生錯誤: {e}")
        
        # 特徵重要性分析
        logger.info(f"選擇的特徵數量: {len(feature_columns)}")
        
        # 處理缺失值和異常值
        df[feature_columns] = df[feature_columns].fillna(df[feature_columns].median())
        
        # 異常值處理
        for col in feature_columns:
            q1 = df[col].quantile(0.25)
            q3 = df[col].quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            df[col] = np.clip(df[col], lower_bound, upper_bound)
        
        return feature_columns
    
    def create_sequences(self, df, feature_columns):
        """創建時間序列資料"""
        logger.info("正在創建時間序列...")
        
        X, y = [], []
        sequence_length = self.config['sequence_length']
        
        # 按標籤分組創建序列
        for label in df['標籤'].unique():
            label_df = df[df['標籤'] == label].reset_index(drop=True)
            
            # 滑動窗口創建序列
            for i in range(len(label_df) - sequence_length + 1):
                sequence = label_df.iloc[i:i+sequence_length][feature_columns].values
                
                # 檢查序列有效性
                if not np.any(np.isnan(sequence)) and not np.any(np.isinf(sequence)):
                    X.append(sequence.astype(np.float32))
                    y.append(label)
        
        if len(X) == 0:
            raise ValueError("沒有創建任何有效序列")
        
        X = np.array(X, dtype=np.float32)
        y = np.array(y)
        
        logger.info(f"✅ 創建序列數量: {len(X)}")
        logger.info(f"✅ 序列形狀: {X.shape}")
        
        return X, y
    
    def prepare_data(self):
        """準備訓練資料"""
        try:
            # 載入資料
            df, feature_columns = self.load_and_preprocess_data()
            
            # 創建序列
            X, y = self.create_sequences(df, feature_columns)
            
            # 標準化特徵
            logger.info("正在標準化特徵...")
            X_reshaped = X.reshape(-1, X.shape[-1])
            X_scaled = self.scaler.fit_transform(X_reshaped)
            X = X_scaled.reshape(X.shape).astype(np.float32)
            
            # 編碼標籤
            y_encoded = self.label_encoder.fit_transform(y)
            
            # 分割資料
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_encoded, test_size=0.2, random_state=42, 
                stratify=y_encoded if len(np.unique(y_encoded)) > 1 else None
            )
            
            logger.info(f"✅ 訓練集形狀: {X_train.shape}")
            logger.info(f"✅ 測試集形狀: {X_test.shape}")
            
            return X_train, X_test, y_train, y_test, feature_columns
            
        except Exception as e:
            logger.error(f"❌ 資料準備失敗: {e}")
            raise
    
    def create_data_loaders(self, X_train, X_test, y_train, y_test):
        """創建資料載入器"""
        batch_size = self.config['batch_size']
        
        # 計算類別權重
        class_weights = None
        if self.config['use_class_weights']:
            class_counts = np.bincount(y_train)
            class_weights = 1.0 / class_counts
            class_weights = class_weights / class_weights.sum() * len(class_counts)
            
            # 創建樣本權重
            sample_weights = class_weights[y_train]
            sampler = WeightedRandomSampler(
                weights=sample_weights,
                num_samples=len(sample_weights),
                replacement=True
            )
        else:
            sampler = None
        
        # 創建資料集
        train_dataset = SOPDataset(
            X_train, y_train, 
            augment=self.config['use_data_augmentation']
        )
        test_dataset = SOPDataset(X_test, y_test, augment=False)
        
        # 創建載入器
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            sampler=sampler,
            shuffle=(sampler is None),
            num_workers=0,
            pin_memory=torch.cuda.is_available()
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=torch.cuda.is_available()
        )
        
        return train_loader, test_loader, class_weights
    
    def build_model(self, input_size, num_classes):
        """建立模型"""
        logger.info(f"建立模型 - 輸入大小: {input_size}, 類別數: {num_classes}")
        
        model = ImprovedSOPLSTM(
            input_size=input_size,
            hidden_size=self.config['hidden_size'],
            num_layers=self.config['num_layers'],
            num_classes=num_classes,
            dropout=self.config['dropout']
        ).to(self.device)
        
        # 計算參數數量
        total_params = sum(p.numel() for p in model.parameters())
        logger.info(f"✅ 模型參數數量: {total_params:,}")
        
        return model
    
    def train_model(self, train_loader, test_loader, num_classes, input_size, class_weights=None):
        """訓練模型"""
        logger.info("開始訓練模型...")
        
        # 建立模型
        self.model = self.build_model(input_size, num_classes)
        
        # 損失函數
        if class_weights is not None:
            weight_tensor = torch.FloatTensor(class_weights).to(self.device)
            criterion = nn.CrossEntropyLoss(weight=weight_tensor)
        else:
            criterion = nn.CrossEntropyLoss()
        
        # 優化器
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # 學習率調度器
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=10, T_mult=2, eta_min=1e-6
        )
        
        # 訓練記錄
        history = {
            'train_losses': [],
            'train_accuracies': [],
            'val_losses': [],
            'val_accuracies': [],
            'learning_rates': []
        }
        
        best_val_acc = 0
        patience_counter = 0
        
        for epoch in range(self.config['epochs']):
            # 訓練階段
            train_loss, train_acc = self._train_epoch(train_loader, criterion, optimizer)
            
            # 驗證階段
            val_loss, val_acc = self._validate_epoch(test_loader, criterion)
            
            # 記錄歷史
            history['train_losses'].append(train_loss)
            history['train_accuracies'].append(train_acc)
            history['val_losses'].append(val_loss)
            history['val_accuracies'].append(val_acc)
            history['learning_rates'].append(optimizer.param_groups[0]['lr'])
            
            # 學習率調整
            scheduler.step()
            
            # 早停檢查
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                torch.save(self.model.state_dict(), f'{self.output_dir}/best_model.pth')
            else:
                patience_counter += 1
            
            # 記錄進度
            if (epoch + 1) % 10 == 0:
                logger.info(f'Epoch [{epoch+1}/{self.config["epochs"]}]')
                logger.info(f'  訓練: 損失={train_loss:.4f}, 準確率={train_acc:.2f}%')
                logger.info(f'  驗證: 損失={val_loss:.4f}, 準確率={val_acc:.2f}%')
            
            # 早停
            if patience_counter >= self.config['early_stopping_patience']:
                logger.info(f"早停於第 {epoch+1} epoch (最佳驗證準確率: {best_val_acc:.2f}%)")
                break
        
        # 載入最佳模型
        self.model.load_state_dict(torch.load(f'{self.output_dir}/best_model.pth'))
        self.training_history = history
        
        logger.info(f"訓練完成！最佳驗證準確率: {best_val_acc:.2f}%")
        return history
    
    def _train_epoch(self, train_loader, criterion, optimizer):
        """訓練一個 epoch"""
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            optimizer.zero_grad()
            output = self.model(data)
            loss = criterion(output, target)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
        
        return total_loss / len(train_loader), 100 * correct / total
    
    def _validate_epoch(self, test_loader, criterion):
        """驗證一個 epoch"""
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                loss = criterion(output, target)
                
                total_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        return total_loss / len(test_loader), 100 * correct / total
    
    def cross_validation(self, X, y):
        """交叉驗證"""
        logger.info("開始交叉驗證...")
        
        kfold = StratifiedKFold(n_splits=self.config['cross_validation_folds'], shuffle=True, random_state=42)
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(kfold.split(X, y)):
            logger.info(f"交叉驗證 Fold {fold + 1}/{self.config['cross_validation_folds']}")
            
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # 創建資料載入器
            train_loader_fold, val_loader_fold, class_weights = self.create_data_loaders(
                X_train_fold, X_val_fold, y_train_fold, y_val_fold
            )
            
            # 訓練模型
            fold_history = self.train_model(
                train_loader_fold, val_loader_fold, 
                len(np.unique(y)), X.shape[2], class_weights
            )
            
            # 評估
            val_acc = self.evaluate_model(val_loader_fold, verbose=False)
            cv_scores.append(val_acc)
            
            logger.info(f"Fold {fold + 1} 準確率: {val_acc:.4f}")
        
        mean_cv_score = np.mean(cv_scores)
        std_cv_score = np.std(cv_scores)
        
        logger.info(f"交叉驗證結果: {mean_cv_score:.4f} (+/- {std_cv_score * 2:.4f})")
        
        return cv_scores
    
    def evaluate_model(self, test_loader, verbose=True):
        """評估模型"""
        if verbose:
            logger.info("正在評估模型...")
        
        self.model.eval()
        all_predictions = []
        all_targets = []
        all_probabilities = []
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                probabilities = torch.softmax(output, dim=1)
                _, predicted = torch.max(output, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # 計算指標
        accuracy = accuracy_score(all_targets, all_predictions)
        f1 = f1_score(all_targets, all_predictions, average='weighted')
        
        if verbose:
            logger.info(f"測試集準確率: {accuracy:.4f}")
            logger.info(f"F1 分數: {f1:.4f}")
            
            # 分類報告
            target_names = self.label_encoder.classes_
            print("\n分類報告:")
            print(classification_report(all_targets, all_predictions, 
                                      target_names=target_names, zero_division=0))
        
        # 保存評估結果
        self.model_metrics = {
            'accuracy': accuracy,
            'f1_score': f1,
            'predictions': all_predictions,
            'targets': all_targets,
            'probabilities': all_probabilities
        }
        
        if verbose:
            self.plot_confusion_matrix(all_targets, all_predictions)
        
        return accuracy
    
    def plot_confusion_matrix(self, y_true, y_pred):
        """繪製混淆矩陣"""
        cm = confusion_matrix(y_true, y_pred)
        target_names = self.label_encoder.classes_
        
        plt.figure(figsize=(20, 16))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=target_names, yticklabels=target_names,
                   cbar_kws={'label': '預測數量'})
        plt.title('增強型 SOP LSTM 混淆矩陣', fontsize=18, fontweight='bold', pad=20)
        plt.ylabel('真實標籤', fontsize=16, fontweight='bold')
        plt.xlabel('預測標籤', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45, ha='right', fontsize=11)
        plt.yticks(rotation=0, fontsize=11)
        
        # 添加準確率信息
        accuracy = np.sum(np.diag(cm)) / np.sum(cm)
        plt.figtext(0.02, 0.02, f'總體準確率: {accuracy:.2%}',
                   fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/混淆矩陣.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_training_history(self, history):
        """繪製訓練歷史"""
        plt.figure(figsize=(24, 8))
        epochs = range(1, len(history['train_losses']) + 1)
        
        # 損失圖
        plt.subplot(1, 4, 1)
        plt.plot(epochs, history['train_losses'], 'b-', label='訓練損失', linewidth=2)
        plt.plot(epochs, history['val_losses'], 'r-', label='驗證損失', linewidth=2)
        plt.title('損失變化', fontsize=14, fontweight='bold')
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('損失值', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 準確率圖
        plt.subplot(1, 4, 2)
        plt.plot(epochs, history['train_accuracies'], 'b-', label='訓練準確率', linewidth=2)
        plt.plot(epochs, history['val_accuracies'], 'r-', label='驗證準確率', linewidth=2)
        plt.title('準確率變化', fontsize=14, fontweight='bold')
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('準確率 (%)', fontsize=12)
        plt.legend()
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 學習率圖
        plt.subplot(1, 4, 3)
        plt.plot(epochs, history['learning_rates'], 'g-', label='學習率', linewidth=2)
        plt.title('學習率變化', fontsize=14, fontweight='bold')
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('學習率', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.yscale('log')
        
        # 訓練與驗證差異圖
        plt.subplot(1, 4, 4)
        loss_diff = np.array(history['val_losses']) - np.array(history['train_losses'])
        acc_diff = np.array(history['val_accuracies']) - np.array(history['train_accuracies'])
        plt.plot(epochs, loss_diff, 'purple', label='損失差異', linewidth=2)
        plt.plot(epochs, acc_diff, 'orange', label='準確率差異', linewidth=2)
        plt.title('過擬合監控', fontsize=14, fontweight='bold')
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('驗證 - 訓練', fontsize=12)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='k', linestyle='--', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/訓練歷史.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_feature_importance(self, feature_columns, X_train, y_train):
        """繪製特徵重要性（使用統計方法）"""
        logger.info("分析特徵重要性...")
        
        # 計算特徵與標籤的相關性
        feature_importance = []
        
        for i, feature in enumerate(feature_columns):
            # 計算每個特徵在序列中的平均值
            feature_values = X_train[:, :, i].mean(axis=1)
            
            # 計算與標籤的相關性（使用方差分析）
            from scipy.stats import f_oneway
            groups = [feature_values[y_train == label] for label in np.unique(y_train)]
            try:
                f_stat, p_value = f_oneway(*groups)
                importance = f_stat if not np.isnan(f_stat) else 0
            except:
                importance = 0
            
            feature_importance.append((feature, importance))
        
        # 排序並繪圖
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        top_features = feature_importance[:20]  # 取前20個重要特徵
        
        features, scores = zip(*top_features)
        
        plt.figure(figsize=(12, 8))
        bars = plt.barh(range(len(features)), scores, color='skyblue', alpha=0.8)
        plt.yticks(range(len(features)), features)
        plt.xlabel('重要性分數 (F-統計量)', fontsize=12)
        plt.title('特徵重要性分析 (Top 20)', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3, axis='x')
        
        # 添加數值標籤
        for i, (bar, score) in enumerate(zip(bars, scores)):
            plt.text(bar.get_width() + max(scores) * 0.01, bar.get_y() + bar.get_height()/2,
                    f'{score:.2f}', ha='left', va='center', fontsize=9)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/特徵重要性.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def save_model_and_metadata(self, feature_columns):
        """保存模型和元數據"""
        logger.info("正在保存模型和元數據...")
        
        # 保存模型
        model_path = f'{self.output_dir}/enhanced_sop_lstm_model.pth'
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config,
            'feature_columns': feature_columns,
            'label_encoder': self.label_encoder,
            'scaler': self.scaler,
            'training_history': self.training_history,
            'model_metrics': self.model_metrics
        }, model_path)
        
        # 保存標籤編碼器
        joblib.dump(self.label_encoder, f'{self.output_dir}/label_encoder.pkl')
        
        # 保存特徵縮放器
        joblib.dump(self.scaler, f'{self.output_dir}/scaler.pkl')
        
        # 保存配置
        with open(f'{self.output_dir}/config.json', 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        # 保存特徵列表
        with open(f'{self.output_dir}/feature_columns.json', 'w', encoding='utf-8') as f:
            json.dump(feature_columns, f, indent=2, ensure_ascii=False)
        
        # 保存模型摘要
        model_summary = {
            'timestamp': datetime.now().isoformat(),
            'model_type': 'Enhanced SOP LSTM',
            'num_features': len(feature_columns),
            'num_classes': len(self.label_encoder.classes_),
            'sequence_length': self.config['sequence_length'],
            'model_parameters': sum(p.numel() for p in self.model.parameters()),
            'best_accuracy': self.model_metrics.get('accuracy', 0),
            'best_f1_score': self.model_metrics.get('f1_score', 0),
            'classes': self.label_encoder.classes_.tolist()
        }
        
        with open(f'{self.output_dir}/model_summary.json', 'w', encoding='utf-8') as f:
            json.dump(model_summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 模型已保存至: {model_path}")
    
    def load_model(self, model_path):
        """載入預訓練模型"""
        logger.info(f"正在載入模型: {model_path}")
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 載入配置
        self.config = checkpoint['config']
        feature_columns = checkpoint['feature_columns']
        
        # 載入編碼器和縮放器
        self.label_encoder = checkpoint['label_encoder']
        self.scaler = checkpoint['scaler']
        
        # 建立和載入模型
        num_classes = len(self.label_encoder.classes_)
        input_size = len(feature_columns)
        
        self.model = self.build_model(input_size, num_classes)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # 載入歷史記錄
        self.training_history = checkpoint.get('training_history', {})
        self.model_metrics = checkpoint.get('model_metrics', {})
        
        logger.info("✅ 模型載入成功")
        return feature_columns
    
    def predict(self, X):
        """預測新樣本"""
        self.model.eval()
        
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X).to(self.device)
            outputs = self.model(X_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            _, predicted = torch.max(outputs, 1)
            
            predicted_labels = self.label_encoder.inverse_transform(predicted.cpu().numpy())
            probabilities = probabilities.cpu().numpy()
        
        return predicted_labels, probabilities
    
    def predict_single_sequence(self, sequence):
        """預測單個序列"""
        if len(sequence.shape) == 2:
            sequence = sequence.reshape(1, sequence.shape[0], sequence.shape[1])
        
        predicted_labels, probabilities = self.predict(sequence)
        return predicted_labels[0], probabilities[0]
    
    def generate_detailed_report(self):
        """生成詳細報告"""
        logger.info("正在生成詳細報告...")
        
        report = {
            'model_info': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'model_type': 'Enhanced SOP LSTM with Attention',
                'framework': 'PyTorch',
                'device': str(self.device)
            },
            'data_info': {
                'csv_file': self.csv_file,
                'sequence_length': self.config['sequence_length'],
                'num_features': len(self.scaler.feature_names_in_) if hasattr(self.scaler, 'feature_names_in_') else 'N/A',
                'num_classes': len(self.label_encoder.classes_),
                'class_names': self.label_encoder.classes_.tolist()
            },
            'model_architecture': {
                'hidden_size': self.config['hidden_size'],
                'num_layers': self.config['num_layers'],
                'dropout': self.config['dropout'],
                'bidirectional': True,
                'attention_heads': 4,
                'total_parameters': sum(p.numel() for p in self.model.parameters()) if self.model else 0
            },
            'training_config': {
                'batch_size': self.config['batch_size'],
                'learning_rate': self.config['learning_rate'],
                'weight_decay': self.config['weight_decay'],
                'epochs': self.config['epochs'],
                'early_stopping_patience': self.config['early_stopping_patience'],
                'data_augmentation': self.config['use_data_augmentation'],
                'class_weights': self.config['use_class_weights']
            },
            'performance_metrics': self.model_metrics,
            'training_history': self.training_history
        }
        
        # 保存報告
        with open(f'{self.output_dir}/detailed_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成 Markdown 報告
        self.generate_markdown_report(report)
        
        logger.info("✅ 詳細報告已生成")
    
    def generate_markdown_report(self, report):
        """生成 Markdown 格式報告"""
        markdown_content = f"""# 增強型 SOP LSTM 模型報告

## 模型信息
- **時間戳記**: {report['model_info']['timestamp']}
- **模型類型**: {report['model_info']['model_type']}
- **框架**: {report['model_info']['framework']}
- **設備**: {report['model_info']['device']}

## 數據信息
- **數據文件**: {report['data_info']['csv_file']}
- **序列長度**: {report['data_info']['sequence_length']}
- **特徵數量**: {report['data_info']['num_features']}
- **類別數量**: {report['data_info']['num_classes']}
- **類別名稱**: {', '.join(report['data_info']['class_names'])}

## 模型架構
- **隱藏層大小**: {report['model_architecture']['hidden_size']}
- **LSTM 層數**: {report['model_architecture']['num_layers']}
- **Dropout 比率**: {report['model_architecture']['dropout']}
- **雙向 LSTM**: {report['model_architecture']['bidirectional']}
- **注意力頭數**: {report['model_architecture']['attention_heads']}
- **總參數數量**: {report['model_architecture']['total_parameters']:,}

## 訓練配置
- **批次大小**: {report['training_config']['batch_size']}
- **學習率**: {report['training_config']['learning_rate']}
- **權重衰減**: {report['training_config']['weight_decay']}
- **最大訓練輪次**: {report['training_config']['epochs']}
- **早停耐心**: {report['training_config']['early_stopping_patience']}
- **數據增強**: {report['training_config']['data_augmentation']}
- **類別權重**: {report['training_config']['class_weights']}

## 性能指標
- **準確率**: {report['performance_metrics'].get('accuracy', 0):.4f}
- **F1 分數**: {report['performance_metrics'].get('f1_score', 0):.4f}

## 文件輸出
- 模型文件: `enhanced_sop_lstm_model.pth`
- 混淆矩陣: `混淆矩陣.png`
- 訓練歷史: `訓練歷史.png`
- 特徵重要性: `特徵重要性.png`
- 詳細報告: `detailed_report.json`
"""
        
        with open(f'{self.output_dir}/README.md', 'w', encoding='utf-8') as f:
            f.write(markdown_content)
    
    def run_complete_pipeline(self):
        """運行完整的訓練管道"""
        logger.info("🚀 開始運行完整的增強型 SOP LSTM 訓練管道")
        logger.info("=" * 70)
        
        start_time = time.time()
        
        try:
            # 1. 準備數據
            logger.info("📊 步驟 1: 準備數據")
            X_train, X_test, y_train, y_test, feature_columns = self.prepare_data()
            
            # 2. 創建數據載入器
            logger.info("🔄 步驟 2: 創建數據載入器")
            train_loader, test_loader, class_weights = self.create_data_loaders(
                X_train, X_test, y_train, y_test
            )
            
            # 3. 訓練模型
            logger.info("🎯 步驟 3: 訓練模型")
            history = self.train_model(
                train_loader, test_loader, 
                len(np.unique(y_train)), X_train.shape[2], class_weights
            )
            
            # 4. 評估模型
            logger.info("📈 步驟 4: 評估模型")
            test_accuracy = self.evaluate_model(test_loader)
            
            # 5. 生成可視化
            logger.info("📊 步驟 5: 生成可視化")
            self.plot_training_history(history)
            self.plot_feature_importance(feature_columns, X_train, y_train)
            
            # 6. 保存模型
            logger.info("💾 步驟 6: 保存模型和元數據")
            self.save_model_and_metadata(feature_columns)
            
            # 7. 生成報告
            logger.info("📋 步驟 7: 生成詳細報告")
            self.generate_detailed_report()
            
            # 8. 交叉驗證（可選）
            if self.config.get('run_cross_validation', False):
                logger.info("🔄 步驟 8: 交叉驗證")
                X_all = np.concatenate([X_train, X_test], axis=0)
                y_all = np.concatenate([y_train, y_test], axis=0)
                cv_scores = self.cross_validation(X_all, y_all)
            
            # 計算總時間
            total_time = time.time() - start_time
            
            logger.info("=" * 70)
            logger.info("🎉 訓練管道完成!")
            logger.info(f"⏱️  總耗時: {total_time:.2f} 秒")
            logger.info(f"🎯 最佳測試準確率: {test_accuracy:.4f}")
            logger.info(f"📁 輸出目錄: {self.output_dir}")
            logger.info("=" * 70)
            
            return {
                'test_accuracy': test_accuracy,
                'training_time': total_time,
                'model_path': f'{self.output_dir}/enhanced_sop_lstm_model.pth',
                'feature_columns': feature_columns
            }
            
        except Exception as e:
            logger.error(f"❌ 訓練管道失敗: {e}")
            raise

def main():
    """主函數"""
    print("🤖 增強型 SOP LSTM 分類器")
    print("=" * 50)
    
    # 自定義配置
    custom_config = {
        'sequence_length': 15,
        'hidden_size': 128,
        'num_layers': 2,
        'dropout': 0.3,
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 100,
        'early_stopping_patience': 15,
        'weight_decay': 1e-4,
        'use_class_weights': True,
        'use_data_augmentation': True,
        'cross_validation_folds': 5,
        'run_cross_validation': False  # 設為 True 啟用交叉驗證
    }
    
    # 創建訓練器
    trainer = EnhancedSOPTrainer(
        csv_file='sop_features_已標記_繁體中文.csv',
        config=custom_config
    )
    
    # 運行完整管道
    try:
        results = trainer.run_complete_pipeline()
        print("\n🎉 訓練成功完成!")
        print(f"📊 測試準確率: {results['test_accuracy']:.4f}")
        print(f"⏱️  訓練時間: {results['training_time']:.2f} 秒")
        print(f"💾 模型已保存至: {results['model_path']}")
        
    except Exception as e:
        print(f"\n❌ 訓練失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

# 使用示例
"""
# 1. 基本使用
trainer = EnhancedSOPTrainer('your_data.csv')
results = trainer.run_complete_pipeline()

# 2. 自定義配置
custom_config = {
    'sequence_length': 20,
    'hidden_size': 256,
    'batch_size': 64,
    'learning_rate': 0.0005
}
trainer = EnhancedSOPTrainer('your_data.csv', config=custom_config)
results = trainer.run_complete_pipeline()

# 3. 載入預訓練模型並預測
trainer = EnhancedSOPTrainer()
feature_columns = trainer.load_model('enhanced_sop_models/enhanced_sop_lstm_model.pth')
predictions, probabilities = trainer.predict(your_sequence_data)
"""