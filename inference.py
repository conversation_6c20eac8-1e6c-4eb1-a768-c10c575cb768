
import cv2
import mediapipe as mp
import numpy as np
from tensorflow.keras.models import load_model
from sklearn.preprocessing import LabelEncoder
import joblib
import json
import os
from ultralytics import YOLO

# 載入模型
model = load_model('sop_model.h5')
label_encoder = joblib.load('label_encoder.pkl')

# 初始化
mp_pose = mp.solutions.pose
# 改進的 MediaPipe 設定
pose = mp_pose.Pose(
    static_image_mode=False,
    model_complexity=2,
    enable_segmentation=False,
    min_detection_confidence=0.3,
    min_tracking_confidence=0.3
)
yolo_model = YOLO('yolov8n.pt')

def load_task_area():
    """載入作業區域設定"""
    config_file = 'task_area_config.json'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def is_person_in_task_area(person_box, task_area, overlap_threshold=0.3):
    """判斷人員是否在作業區域內"""
    if task_area is None:
        return True

    px1, py1, px2, py2 = person_box
    tx1, ty1, tx2, ty2 = task_area

    # 計算重疊區域
    overlap_x1 = max(px1, tx1)
    overlap_y1 = max(py1, ty1)
    overlap_x2 = min(px2, tx2)
    overlap_y2 = min(py2, ty2)

    if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
        return False

    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
    person_area = (px2 - px1) * (py2 - py1)
    overlap_ratio = overlap_area / person_area if person_area > 0 else 0

    return overlap_ratio >= overlap_threshold

def find_main_worker(frame, task_area):
    """找出主要作業員工 - 嚴格版本"""
    results = yolo_model.predict(frame, conf=0.5, verbose=False)
    people = []

    if len(results) > 0 and results[0].boxes is not None:
        for box in results[0].boxes:
            if int(box.cls[0]) == 0:  # person class
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0])
                person_box = [int(x1), int(y1), int(x2), int(y2)]
                people.append({
                    'box': person_box,
                    'confidence': confidence,
                    'in_task_area': is_person_in_task_area(person_box, task_area)
                })

    if not people:
        return None

    if task_area is None:
        best_person = max(people, key=lambda p: p['confidence'])
        return best_person['box']

    # 只考慮在作業區域內的人員
    people_in_area = [p for p in people if p['in_task_area']]

    if people_in_area:
        best_person = max(people_in_area, key=lambda p: p['confidence'])
        return best_person['box']
    else:
        # 沒有人在作業區域內，返回None
        return None

def extract_person_roi_advanced(frame, person_box):
    """改進的人員ROI提取和姿勢偵測"""
    if person_box is None:
        return None

    h, w = frame.shape[:2]
    x1, y1, x2, y2 = person_box

    # 嘗試多種 padding 策略
    strategies = [30, 50, 10]

    for padding in strategies:
        roi_x1 = max(0, x1 - padding)
        roi_y1 = max(0, y1 - padding)
        roi_x2 = min(w, x2 + padding)
        roi_y2 = min(h, y2 + padding)

        person_roi = frame[roi_y1:roi_y2, roi_x1:roi_x2]

        if person_roi.shape[0] >= 50 and person_roi.shape[1] >= 50:
            rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
            results = pose.process(rgb_roi)

            if results.pose_landmarks:
                return results.pose_landmarks

    # 嘗試全畫面偵測
    rgb_full = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = pose.process(rgb_full)

    if results.pose_landmarks:
        # 檢查姿勢是否在人員附近
        person_center = ((x1 + x2) / 2, (y1 + y2) / 2)
        landmarks = results.pose_landmarks.landmark
        pose_x = sum([lm.x for lm in landmarks]) / len(landmarks) * w
        pose_y = sum([lm.y for lm in landmarks]) / len(landmarks) * h
        pose_center = (pose_x, pose_y)

        dist = ((person_center[0] - pose_center[0]) ** 2 + (person_center[1] - pose_center[1]) ** 2) ** 0.5

        if dist < 100:
            return results.pose_landmarks

    return None

# 載入作業區域設定
task_area = load_task_area()
if task_area:
    print(f"✅ 載入作業區域設定: {task_area}")
    print("🎯 將只分析主要作業員工的動作")
else:
    print("⚠️ 未找到作業區域設定，將分析第一個偵測到的人員")

cap = cv2.VideoCapture('1.mp4')
frame_count = 0
detection_count = 0

while cap.isOpened():
    ret, frame = cap.read()
    if not ret:
        break

    frame_count += 1

    # 找出主要作業員工
    main_worker_box = find_main_worker(frame, task_area)

    if main_worker_box is not None:
        # 使用改進的姿勢偵測
        pose_landmarks = extract_person_roi_advanced(frame, main_worker_box)

        if pose_landmarks is not None:
            features = []
            for lm in pose_landmarks.landmark:
                features += [lm.x, lm.y, lm.z, lm.visibility]

            X = np.array(features).reshape(1, 1, -1)
            y_pred = model.predict(X, verbose=0)
            label = label_encoder.inverse_transform([np.argmax(y_pred)])[0]

            print(f"幀 {frame_count}: 辨識動作 - {label}")
            detection_count += 1

cap.release()
print(f"\n📊 推論完成:")
print(f"總幀數: {frame_count}")
print(f"成功辨識: {detection_count}")
print(f"辨識率: {detection_count/frame_count*100:.1f}%")
