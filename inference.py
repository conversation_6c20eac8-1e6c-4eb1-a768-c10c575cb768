import cv2
import mediapipe as mp
import numpy as np
from tensorflow.keras.models import load_model
from sklearn.preprocessing import LabelEncoder
import joblib
import json
import os
import time
from datetime import datetime
from collections import deque, Counter
import logging
from ultralytics import YOLO
import pandas as pd

class SOPMonitor:
    def __init__(self, config_file='sop_config.json'):
        """初始化SOP監控系統"""
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.load_models()
        self.init_mediapipe()
        self.init_tracking()
        
    def load_config(self, config_file):
        """載入配置檔案"""
        default_config = {
            'model_path': 'sop_model.h5',
            'label_encoder_path': 'label_encoder.pkl',
            'yolo_model': 'yolov8n.pt',
            'task_area_config': 'task_area_config.json',
            'confidence_threshold': 0.5,
            'tracking_buffer_size': 10,
            'violation_threshold': 3,
            'fps_target': 30,
            'enable_alerts': True,
            'alert_cooldown': 30,  # 秒
            'log_level': 'INFO',
            'output_dir': 'sop_output',
            'violation_actions': ['不正確姿勢', '未穿戴防護具', '危險動作'],
            'safe_actions': ['正常作業', '標準姿勢', '安全操作']
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合併預設配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"載入配置檔案失敗: {e}")
        
        return default_config
    
    def setup_logging(self):
        """設定日誌系統"""
        if not os.path.exists(self.config['output_dir']):
            os.makedirs(self.config['output_dir'])
            
        log_file = os.path.join(self.config['output_dir'], 
                               f"sop_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        logging.basicConfig(
            level=getattr(logging, self.config['log_level']),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_models(self):
        """載入所有模型"""
        try:
            self.model = load_model(self.config['model_path'])
            self.label_encoder = joblib.load(self.config['label_encoder_path'])
            self.yolo_model = YOLO(self.config['yolo_model'])
            self.logger.info("✅ 模型載入成功")
        except Exception as e:
            self.logger.error(f"❌ 模型載入失敗: {e}")
            raise
    
    def init_mediapipe(self):
        """初始化MediaPipe"""
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=2,
            enable_segmentation=False,
            min_detection_confidence=0.3,
            min_tracking_confidence=0.3
        )
        self.mp_drawing = mp.solutions.drawing_utils
    
    def init_tracking(self):
        """初始化追蹤相關變數"""
        self.tracking_buffer = deque(maxlen=self.config['tracking_buffer_size'])
        self.violation_count = 0
        self.last_alert_time = 0
        self.stats = {
            'total_frames': 0,
            'detected_frames': 0,
            'violations': 0,
            'safe_actions': 0,
            'start_time': time.time()
        }
        self.violation_log = []
        
    def load_task_area(self):
        """載入作業區域設定"""
        config_file = self.config['task_area_config']
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('task_area', None)
            except:
                return None
        return None
    
    def is_person_in_task_area(self, person_box, task_area, overlap_threshold=0.3):
        """判斷人員是否在作業區域內"""
        if task_area is None:
            return True

        px1, py1, px2, py2 = person_box
        tx1, ty1, tx2, ty2 = task_area

        # 計算重疊區域
        overlap_x1 = max(px1, tx1)
        overlap_y1 = max(py1, ty1)
        overlap_x2 = min(px2, tx2)
        overlap_y2 = min(py2, ty2)

        if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
            return False

        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
        person_area = (px2 - px1) * (py2 - py1)
        overlap_ratio = overlap_area / person_area if person_area > 0 else 0

        return overlap_ratio >= overlap_threshold
    
    def find_main_worker(self, frame, task_area):
        """找出主要作業員工"""
        results = self.yolo_model.predict(frame, conf=self.config['confidence_threshold'], verbose=False)
        people = []

        if len(results) > 0 and results[0].boxes is not None:
            for box in results[0].boxes:
                if int(box.cls[0]) == 0:  # person class
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    person_box = [int(x1), int(y1), int(x2), int(y2)]
                    people.append({
                        'box': person_box,
                        'confidence': confidence,
                        'in_task_area': self.is_person_in_task_area(person_box, task_area)
                    })

        if not people:
            return None

        if task_area is None:
            best_person = max(people, key=lambda p: p['confidence'])
            return best_person['box']

        # 優先選擇在作業區域內的人員
        people_in_area = [p for p in people if p['in_task_area']]

        if people_in_area:
            best_person = max(people_in_area, key=lambda p: p['confidence'])
            return best_person['box']
        else:
            return None
    
    def extract_pose_features(self, frame, person_box):
        """提取姿勢特徵"""
        if person_box is None:
            return None

        h, w = frame.shape[:2]
        x1, y1, x2, y2 = person_box

        # 多種padding策略
        strategies = [30, 50, 10]

        for padding in strategies:
            roi_x1 = max(0, x1 - padding)
            roi_y1 = max(0, y1 - padding)
            roi_x2 = min(w, x2 + padding)
            roi_y2 = min(h, y2 + padding)

            person_roi = frame[roi_y1:roi_y2, roi_x1:roi_x2]

            if person_roi.shape[0] >= 50 and person_roi.shape[1] >= 50:
                rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
                results = self.pose.process(rgb_roi)

                if results.pose_landmarks:
                    return results.pose_landmarks

        # 全畫面偵測
        rgb_full = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.pose.process(rgb_full)

        if results.pose_landmarks:
            # 檢查姿勢是否在人員附近
            person_center = ((x1 + x2) / 2, (y1 + y2) / 2)
            landmarks = results.pose_landmarks.landmark
            pose_x = sum([lm.x for lm in landmarks]) / len(landmarks) * w
            pose_y = sum([lm.y for lm in landmarks]) / len(landmarks) * h
            pose_center = (pose_x, pose_y)

            dist = ((person_center[0] - pose_center[0]) ** 2 + (person_center[1] - pose_center[1]) ** 2) ** 0.5

            if dist < 100:
                return results.pose_landmarks

        return None
    
    def predict_action(self, pose_landmarks):
        """預測動作"""
        if pose_landmarks is None:
            return None, 0.0
        
        features = []
        for lm in pose_landmarks.landmark:
            features += [lm.x, lm.y, lm.z, lm.visibility]

        X = np.array(features).reshape(1, 1, -1)
        y_pred = self.model.predict(X, verbose=0)
        
        confidence = float(np.max(y_pred))
        label = self.label_encoder.inverse_transform([np.argmax(y_pred)])[0]
        
        return label, confidence
    
    def is_violation(self, action):
        """判斷是否為違規動作"""
        return action in self.config['violation_actions']
    
    def smooth_predictions(self, current_action):
        """平滑預測結果"""
        if current_action is None:
            return None
            
        self.tracking_buffer.append(current_action)
        
        if len(self.tracking_buffer) >= 3:
            # 使用多數決
            counter = Counter(self.tracking_buffer)
            most_common = counter.most_common(1)[0]
            if most_common[1] >= 2:  # 至少出現2次
                return most_common[0]
        
        return current_action
    
    def handle_violation(self, action, frame, frame_count):
        """處理違規事件"""
        self.violation_count += 1
        current_time = time.time()
        
        violation_info = {
            'frame': frame_count,
            'action': action,
            'timestamp': datetime.now().isoformat(),
            'violation_count': self.violation_count
        }
        
        self.violation_log.append(violation_info)
        self.stats['violations'] += 1
        
        # 警報冷卻檢查
        if (current_time - self.last_alert_time) >= self.config['alert_cooldown']:
            if self.config['enable_alerts']:
                self.logger.warning(f"🚨 違規警報: {action} (幀: {frame_count})")
                self.last_alert_time = current_time
        
        # 連續違規檢查
        if self.violation_count >= self.config['violation_threshold']:
            self.logger.error(f"❌ 連續違規超過閾值: {self.violation_count}")
            
    def draw_annotations(self, frame, person_box, pose_landmarks, action, confidence, task_area):
        """繪製註解"""
        if person_box is not None:
            x1, y1, x2, y2 = person_box
            
            # 繪製人員框
            color = (0, 255, 0) if not self.is_violation(action) else (0, 0, 255)
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            
            # 繪製動作標籤
            if action:
                label_text = f"{action} ({confidence:.2f})"
                cv2.putText(frame, label_text, (x1, y1-10), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 繪製姿勢骨架
        if pose_landmarks is not None:
            self.mp_drawing.draw_landmarks(
                frame, pose_landmarks, self.mp_pose.POSE_CONNECTIONS,
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self.mp_drawing.DrawingSpec(color=(0, 0, 255), thickness=2)
            )
        
        # 繪製作業區域
        if task_area is not None:
            tx1, ty1, tx2, ty2 = task_area
            cv2.rectangle(frame, (tx1, ty1), (tx2, ty2), (255, 255, 0), 2)
            cv2.putText(frame, "Task Area", (tx1, ty1-10), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 繪製統計資訊
        self.draw_stats(frame)
        
        return frame
    
    def draw_stats(self, frame):
        """繪製統計資訊"""
        h, w = frame.shape[:2]
        
        # 背景
        cv2.rectangle(frame, (10, 10), (300, 120), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (300, 120), (255, 255, 255), 2)
        
        # 統計文字
        stats_text = [
            f"Total Frames: {self.stats['total_frames']}",
            f"Detected: {self.stats['detected_frames']}",
            f"Violations: {self.stats['violations']}",
            f"Safe Actions: {self.stats['safe_actions']}"
        ]
        
        for i, text in enumerate(stats_text):
            cv2.putText(frame, text, (20, 30 + i*20), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def save_report(self):
        """儲存報告"""
        report_file = os.path.join(self.config['output_dir'], 
                                  f"sop_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        runtime = time.time() - self.stats['start_time']
        detection_rate = (self.stats['detected_frames'] / self.stats['total_frames'] * 100) if self.stats['total_frames'] > 0 else 0
        
        report = {
            'summary': {
                'total_frames': self.stats['total_frames'],
                'detected_frames': self.stats['detected_frames'],
                'detection_rate': f"{detection_rate:.1f}%",
                'total_violations': self.stats['violations'],
                'safe_actions': self.stats['safe_actions'],
                'runtime': f"{runtime:.1f}s"
            },
            'violations': self.violation_log,
            'config': self.config
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 報告已儲存: {report_file}")
        
        # 也儲存CSV格式
        if self.violation_log:
            csv_file = os.path.join(self.config['output_dir'], 
                                   f"violations_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
            df = pd.DataFrame(self.violation_log)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            self.logger.info(f"📋 違規CSV已儲存: {csv_file}")
    
    def process_video(self, video_path, output_path=None):
        """處理影片"""
        self.logger.info(f"開始處理影片: {video_path}")
        
        # 載入作業區域
        task_area = self.load_task_area()
        if task_area:
            self.logger.info(f"✅ 載入作業區域: {task_area}")
        else:
            self.logger.warning("⚠️ 未找到作業區域設定")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            self.logger.error(f"❌ 無法開啟影片: {video_path}")
            return
        
        # 影片資訊
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        self.logger.info(f"影片資訊: {width}x{height}, {fps}FPS, {total_frames}幀")
        
        # 輸出影片設定
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        else:
            out = None
        
        frame_count = 0
        
        try:
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                self.stats['total_frames'] = frame_count
                
                # 找出主要作業員工
                main_worker_box = self.find_main_worker(frame, task_area)
                
                if main_worker_box is not None:
                    # 姿勢偵測
                    pose_landmarks = self.extract_pose_features(frame, main_worker_box)
                    
                    if pose_landmarks is not None:
                        # 動作預測
                        action, confidence = self.predict_action(pose_landmarks)
                        
                        if action:
                            # 平滑預測
                            smoothed_action = self.smooth_predictions(action)
                            
                            if smoothed_action:
                                self.stats['detected_frames'] += 1
                                
                                # 違規檢查
                                if self.is_violation(smoothed_action):
                                    self.handle_violation(smoothed_action, frame, frame_count)
                                else:
                                    self.stats['safe_actions'] += 1
                                    self.violation_count = 0  # 重置違規計數
                                
                                # 繪製註解
                                frame = self.draw_annotations(frame, main_worker_box, 
                                                           pose_landmarks, smoothed_action, 
                                                           confidence, task_area)
                                
                                self.logger.debug(f"幀 {frame_count}: {smoothed_action} ({confidence:.2f})")
                
                # 儲存輸出影片
                if out:
                    out.write(frame)
                
                # 顯示進度
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    self.logger.info(f"處理進度: {frame_count}/{total_frames} ({progress:.1f}%)")
                
        except KeyboardInterrupt:
            self.logger.info("⏹️ 使用者中斷處理")
        except Exception as e:
            self.logger.error(f"❌ 處理錯誤: {e}")
        finally:
            cap.release()
            if out:
                out.release()
            
            # 儲存報告
            self.save_report()
            
            self.logger.info("📊 處理完成:")
            self.logger.info(f"總幀數: {self.stats['total_frames']}")
            self.logger.info(f"成功辨識: {self.stats['detected_frames']}")
            self.logger.info(f"辨識率: {self.stats['detected_frames']/self.stats['total_frames']*100:.1f}%")
            self.logger.info(f"違規次數: {self.stats['violations']}")
            self.logger.info(f"安全動作: {self.stats['safe_actions']}")

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SOP行為監控系統')
    parser.add_argument('--video', required=True, help='輸入影片路徑')
    parser.add_argument('--output', help='輸出影片路徑')
    parser.add_argument('--config', default='sop_config.json', help='配置檔案路徑')
    
    args = parser.parse_args()
    
    # 建立監控器
    monitor = SOPMonitor(args.config)
    
    # 處理影片
    monitor.process_video(args.video, args.output)

if __name__ == "__main__":
    main()
    