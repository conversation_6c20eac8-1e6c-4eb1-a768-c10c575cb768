import numpy as np
import pandas as pd
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter

class SOPSklearnTrainer:
    def __init__(self, csv_file='sop_features_已標記_繁體中文.csv'):
        self.csv_file = csv_file
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.model = None
        
    def load_and_preprocess_data(self):
        """載入和預處理資料"""
        print("正在載入資料...")
        df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
        
        print(f"原始資料形狀: {df.shape}")
        print(f"標籤分佈:")
        label_counts = df['標籤'].value_counts()
        for label, count in label_counts.items():
            print(f"  {label}: {count} 行")
        
        # 保留所有標籤，不進行過濾
        df_filtered = df.copy()
        print(f"保留所有標籤，資料形狀: {df_filtered.shape}")
        print("注意：保留所有詳細標籤，包括資料較少的標籤")
        
        # 選擇特徵欄位（排除非數值欄位）
        feature_columns = []
        exclude_columns = ['幀數', '時間_秒', '標籤', '時間_分秒']
        
        for col in df_filtered.columns:
            if col not in exclude_columns:
                # 檢查是否為數值欄位
                if df_filtered[col].dtype in ['int64', 'float64', 'bool']:
                    feature_columns.append(col)
                elif col.endswith('正常'):  # 布林欄位
                    # 將布林值轉換為數值
                    df_filtered[col] = df_filtered[col].astype(int)
                    feature_columns.append(col)
        
        print(f"選擇的特徵欄位數量: {len(feature_columns)}")
        print(f"特徵欄位: {feature_columns[:10]}...")  # 顯示前10個
        
        # 處理缺失值
        df_filtered[feature_columns] = df_filtered[feature_columns].fillna(0)
        
        return df_filtered, feature_columns
    
    def prepare_data(self):
        """準備訓練資料"""
        # 載入資料
        df, feature_columns = self.load_and_preprocess_data()
        
        # 提取特徵和標籤
        X = df[feature_columns].values
        y = df['標籤'].values
        
        print(f"特徵矩陣形狀: {X.shape}")
        print(f"標籤數量: {len(y)}")
        
        # 標準化特徵
        print("正在標準化特徵...")
        X_scaled = self.scaler.fit_transform(X)
        
        # 編碼標籤
        print("正在編碼標籤...")
        y_encoded = self.label_encoder.fit_transform(y)
        
        print(f"編碼後的標籤範圍: 0 到 {max(y_encoded)}")
        print(f"標籤類別數量: {len(self.label_encoder.classes_)}")
        
        # 檢查是否有標籤只有1個樣本
        unique_labels, label_counts = np.unique(y_encoded, return_counts=True)
        min_count = np.min(label_counts)
        
        print(f"最少樣本數的標籤有 {min_count} 個樣本")
        
        # 分割訓練和測試集
        if min_count == 1:
            print("警告：某些標籤只有1個樣本，將不使用分層抽樣")
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_encoded, test_size=0.2, random_state=42
            )
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
            )
        
        print(f"訓練集形狀: {X_train.shape}")
        print(f"測試集形狀: {X_test.shape}")
        
        return X_train, X_test, y_train, y_test, feature_columns
    
    def build_model(self):
        """建立隨機森林模型"""
        print("正在建立隨機森林模型...")
        
        # 使用隨機森林，它對各種資料都很穩健
        model = RandomForestClassifier(
            n_estimators=200,
            max_depth=10,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1
        )
        
        return model
    
    def train_model(self, X_train, X_test, y_train, y_test):
        """訓練模型"""
        print("正在訓練模型...")
        
        # 建立模型
        self.model = self.build_model()
        
        # 訓練模型
        self.model.fit(X_train, y_train)
        
        print("模型訓練完成！")
        
        return self.model
    
    def evaluate_model(self, X_test, y_test, feature_columns):
        """評估模型"""
        print("正在評估模型...")
        
        # 預測
        y_pred = self.model.predict(X_test)
        
        # 計算準確率
        accuracy = accuracy_score(y_test, y_pred)
        print(f"測試集準確率: {accuracy:.4f}")
        
        # 顯示分類報告
        target_names = self.label_encoder.classes_
        print("\n分類報告:")
        print(classification_report(y_test, y_pred, target_names=target_names, zero_division=0))
        
        # 特徵重要性
        feature_importance = pd.DataFrame({
            'feature': feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\n前10個最重要的特徵:")
        print(feature_importance.head(10))
        
        # 繪製混淆矩陣
        cm = confusion_matrix(y_test, y_pred)
        plt.figure(figsize=(15, 12))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=target_names, yticklabels=target_names)
        plt.title('混淆矩陣')
        plt.ylabel('真實標籤')
        plt.xlabel('預測標籤')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig('confusion_matrix_sklearn.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 繪製特徵重要性
        plt.figure(figsize=(12, 8))
        top_features = feature_importance.head(15)
        sns.barplot(data=top_features, x='importance', y='feature')
        plt.title('前15個最重要的特徵')
        plt.xlabel('重要性')
        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return accuracy
    
    def save_model_and_encoders(self):
        """保存模型和編碼器"""
        print("正在保存模型和編碼器...")
        
        # 保存模型
        joblib.dump(self.model, 'sop_sklearn_model.pkl')
        
        # 保存編碼器
        joblib.dump(self.label_encoder, 'label_encoder_sklearn.pkl')
        joblib.dump(self.scaler, 'feature_scaler_sklearn.pkl')
        
        print("模型已保存為: sop_sklearn_model.pkl")
        print("標籤編碼器已保存為: label_encoder_sklearn.pkl")
        print("特徵縮放器已保存為: feature_scaler_sklearn.pkl")
    
    def predict_sample(self, X_test, y_test, num_samples=10):
        """預測樣本並顯示結果"""
        print(f"\n預測前 {num_samples} 個測試樣本:")
        
        # 隨機選擇樣本
        indices = np.random.choice(len(X_test), min(num_samples, len(X_test)), replace=False)
        
        for i, idx in enumerate(indices):
            sample = X_test[idx:idx+1]
            true_label = self.label_encoder.inverse_transform([y_test[idx]])[0]
            pred_label = self.label_encoder.inverse_transform(self.model.predict(sample))[0]
            confidence = max(self.model.predict_proba(sample)[0])
            
            status = "✓" if true_label == pred_label else "✗"
            print(f"  {i+1:2d}. 真實: {true_label:<15} | 預測: {pred_label:<15} | 信心度: {confidence:.3f} {status}")
    
    def train(self):
        """完整的訓練流程"""
        try:
            # 準備資料
            X_train, X_test, y_train, y_test, feature_columns = self.prepare_data()
            
            # 訓練模型
            self.train_model(X_train, X_test, y_train, y_test)
            
            # 評估模型
            accuracy = self.evaluate_model(X_test, y_test, feature_columns)
            
            # 預測樣本
            self.predict_sample(X_test, y_test)
            
            # 保存模型
            self.save_model_and_encoders()
            
            print(f"\n訓練完成！最終測試準確率: {accuracy:.4f}")
            
            # 顯示每個標籤的預測情況
            y_pred = self.model.predict(X_test)
            print(f"\n各標籤預測統計:")
            for i, label in enumerate(self.label_encoder.classes_):
                true_count = np.sum(y_test == i)
                pred_count = np.sum(y_pred == i)
                correct_count = np.sum((y_test == i) & (y_pred == i))
                if true_count > 0:
                    precision = correct_count / pred_count if pred_count > 0 else 0
                    recall = correct_count / true_count
                    print(f"  {label}: 真實{true_count} | 預測{pred_count} | 正確{correct_count} | 精確度{precision:.3f} | 召回率{recall:.3f}")
            
        except Exception as e:
            print(f"訓練過程中發生錯誤: {e}")
            import traceback
            traceback.print_exc()

def main():
    print("SOP Scikit-learn 模型訓練器")
    print("=" * 50)
    
    # 創建訓練器
    trainer = SOPSklearnTrainer()
    
    # 開始訓練
    trainer.train()

if __name__ == "__main__":
    main()
