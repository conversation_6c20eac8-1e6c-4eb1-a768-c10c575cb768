import pandas as pd
import csv

# 定義繁體中文標題對應表
chinese_headers = {
    'frame': '幀數',
    'label': '標籤',
    '0_x': '鼻子_X座標', '0_y': '鼻子_Y座標', '0_z': '鼻子_Z座標', '0_visibility': '鼻子_可見度',
    '1_x': '左眼內角_X座標', '1_y': '左眼內角_Y座標', '1_z': '左眼內角_Z座標', '1_visibility': '左眼內角_可見度',
    '2_x': '左眼中心_X座標', '2_y': '左眼中心_Y座標', '2_z': '左眼中心_Z座標', '2_visibility': '左眼中心_可見度',
    '3_x': '左眼外角_X座標', '3_y': '左眼外角_Y座標', '3_z': '左眼外角_Z座標', '3_visibility': '左眼外角_可見度',
    '4_x': '右眼內角_X座標', '4_y': '右眼內角_Y座標', '4_z': '右眼內角_Z座標', '4_visibility': '右眼內角_可見度',
    '5_x': '右眼中心_X座標', '5_y': '右眼中心_Y座標', '5_z': '右眼中心_Z座標', '5_visibility': '右眼中心_可見度',
    '6_x': '右眼外角_X座標', '6_y': '右眼外角_Y座標', '6_z': '右眼外角_Z座標', '6_visibility': '右眼外角_可見度',
    '7_x': '左耳_X座標', '7_y': '左耳_Y座標', '7_z': '左耳_Z座標', '7_visibility': '左耳_可見度',
    '8_x': '右耳_X座標', '8_y': '右耳_Y座標', '8_z': '右耳_Z座標', '8_visibility': '右耳_可見度',
    '9_x': '嘴巴左角_X座標', '9_y': '嘴巴左角_Y座標', '9_z': '嘴巴左角_Z座標', '9_visibility': '嘴巴左角_可見度',
    '10_x': '嘴巴右角_X座標', '10_y': '嘴巴右角_Y座標', '10_z': '嘴巴右角_Z座標', '10_visibility': '嘴巴右角_可見度',
    '11_x': '左肩膀_X座標', '11_y': '左肩膀_Y座標', '11_z': '左肩膀_Z座標', '11_visibility': '左肩膀_可見度',
    '12_x': '右肩膀_X座標', '12_y': '右肩膀_Y座標', '12_z': '右肩膀_Z座標', '12_visibility': '右肩膀_可見度',
    '13_x': '左手肘_X座標', '13_y': '左手肘_Y座標', '13_z': '左手肘_Z座標', '13_visibility': '左手肘_可見度',
    '14_x': '右手肘_X座標', '14_y': '右手肘_Y座標', '14_z': '右手肘_Z座標', '14_visibility': '右手肘_可見度',
    '15_x': '左手腕_X座標', '15_y': '左手腕_Y座標', '15_z': '左手腕_Z座標', '15_visibility': '左手腕_可見度',
    '16_x': '右手腕_X座標', '16_y': '右手腕_Y座標', '16_z': '右手腕_Z座標', '16_visibility': '右手腕_可見度',
    '17_x': '左手小指_X座標', '17_y': '左手小指_Y座標', '17_z': '左手小指_Z座標', '17_visibility': '左手小指_可見度',
    '18_x': '右手小指_X座標', '18_y': '右手小指_Y座標', '18_z': '右手小指_Z座標', '18_visibility': '右手小指_可見度',
    '19_x': '左手食指_X座標', '19_y': '左手食指_Y座標', '19_z': '左手食指_Z座標', '19_visibility': '左手食指_可見度',
    '20_x': '右手食指_X座標', '20_y': '右手食指_Y座標', '20_z': '右手食指_Z座標', '20_visibility': '右手食指_可見度',
    '21_x': '左手拇指_X座標', '21_y': '左手拇指_Y座標', '21_z': '左手拇指_Z座標', '21_visibility': '左手拇指_可見度',
    '22_x': '右手拇指_X座標', '22_y': '右手拇指_Y座標', '22_z': '右手拇指_Z座標', '22_visibility': '右手拇指_可見度',
    '23_x': '左臀部_X座標', '23_y': '左臀部_Y座標', '23_z': '左臀部_Z座標', '23_visibility': '左臀部_可見度',
    '24_x': '右臀部_X座標', '24_y': '右臀部_Y座標', '24_z': '右臀部_Z座標', '24_visibility': '右臀部_可見度',
    '25_x': '左膝蓋_X座標', '25_y': '左膝蓋_Y座標', '25_z': '左膝蓋_Z座標', '25_visibility': '左膝蓋_可見度',
    '26_x': '右膝蓋_X座標', '26_y': '右膝蓋_Y座標', '26_z': '右膝蓋_Z座標', '26_visibility': '右膝蓋_可見度',
    '27_x': '左腳踝_X座標', '27_y': '左腳踝_Y座標', '27_z': '左腳踝_Z座標', '27_visibility': '左腳踝_可見度',
    '28_x': '右腳踝_X座標', '28_y': '右腳踝_Y座標', '28_z': '右腳踝_Z座標', '28_visibility': '右腳踝_可見度',
    '29_x': '左腳跟_X座標', '29_y': '左腳跟_Y座標', '29_z': '左腳跟_Z座標', '29_visibility': '左腳跟_可見度',
    '30_x': '右腳跟_X座標', '30_y': '右腳跟_Y座標', '30_z': '右腳跟_Z座標', '30_visibility': '右腳跟_可見度',
    '31_x': '左腳趾_X座標', '31_y': '左腳趾_Y座標', '31_z': '左腳趾_Z座標', '31_visibility': '左腳趾_可見度',
    '32_x': '右腳趾_X座標', '32_y': '右腳趾_Y座標', '32_z': '右腳趾_Z座標', '32_visibility': '右腳趾_可見度'
}

def convert_csv_to_chinese():
    try:
        # 讀取原始CSV檔案
        print("正在讀取原始CSV檔案...")
        df = pd.read_csv('pose_data.csv', encoding='utf-8')
        
        # 重新命名欄位
        print("正在轉換欄位名稱為繁體中文...")
        df.rename(columns=chinese_headers, inplace=True)
        
        # 保存為新的CSV檔案，使用UTF-8編碼並加上BOM
        print("正在保存新的CSV檔案...")
        df.to_csv('pose_data_繁體中文.csv', index=False, encoding='utf-8-sig')
        
        print("轉換完成！新檔案已保存為 'pose_data_繁體中文.csv'")
        print(f"總共處理了 {len(df)} 行資料")
        print("前5個欄位名稱：", list(df.columns[:5]))
        
    except Exception as e:
        print(f"轉換過程中發生錯誤：{e}")

if __name__ == "__main__":
    convert_csv_to_chinese()
