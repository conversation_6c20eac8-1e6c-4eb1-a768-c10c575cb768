import cv2
import mediapipe as mp
import csv
import json
import os
from ultralytics import YOLO

# 初始化
mp_pose = mp.solutions.pose
# 改進的 MediaPipe 設定，提高對彎腰等困難姿勢的偵測能力
pose = mp_pose.Pose(
    static_image_mode=False,
    model_complexity=2,
    enable_segmentation=False,
    min_detection_confidence=0.3,
    min_tracking_confidence=0.3
)
model = YOLO('yolov8n.pt')

video_path = '2.mp4'
csv_path = 'pose_data_2.csv'
config_file = 'task_area_config.json'

def load_task_area():
    """載入作業區域設定"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def is_person_in_task_area(person_box, task_area, overlap_threshold=0.3):
    """判斷人員是否在作業區域內"""
    if task_area is None:
        return True

    px1, py1, px2, py2 = person_box
    tx1, ty1, tx2, ty2 = task_area

    # 計算重疊區域
    overlap_x1 = max(px1, tx1)
    overlap_y1 = max(py1, ty1)
    overlap_x2 = min(px2, tx2)
    overlap_y2 = min(py2, ty2)

    if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
        person_area = (px2 - px1) * (py2 - py1)
        overlap_ratio = overlap_area / person_area
        return overlap_ratio >= overlap_threshold

    return False

def find_closest_person(person_boxes, task_area):
    """找到最接近作業區域的人員"""
    if not person_boxes:
        return None
    
    if task_area is None:
        return person_boxes[0]  # 如果沒有設定作業區域，返回第一個人
    
    task_center = center(task_area)
    closest_person = None
    min_distance = float('inf')
    
    for person_box in person_boxes:
        person_center = center(person_box)
        dist = distance(person_center, task_center)
        if dist < min_distance:
            min_distance = dist
            closest_person = person_box
    
    return closest_person

def main():
    print("開始處理影片2...")
    print(f"影片路徑: {video_path}")
    print(f"輸出CSV: {csv_path}")
    
    # 載入作業區域設定
    task_area = load_task_area()
    if task_area:
        print(f"作業區域: {task_area}")
    else:
        print("未設定作業區域，將處理所有檢測到的人員")
    
    # 開啟影片
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ 無法開啟影片: {video_path}")
        return
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"影片資訊: {total_frames} 幀, {fps:.2f} FPS")
    
    # 準備CSV檔案
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['frame', 'label']
        
        # 添加所有關鍵點的欄位
        for i in range(33):  # MediaPipe 有33個關鍵點
            fieldnames.extend([f'{i}_x', f'{i}_y', f'{i}_z', f'{i}_visibility'])
        
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        frame_count = 0
        processed_frames = 0
        
        print("開始處理幀...")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # YOLO 人員檢測
            results = model(frame, verbose=False)
            
            person_boxes = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        if int(box.cls[0]) == 0:  # 人員類別
                            person_box = box.xyxy[0].cpu().numpy()
                            person_boxes.append(person_box)
            
            # 找到最適合的人員
            target_person = None
            if person_boxes:
                if task_area:
                    # 優先選擇在作業區域內的人員
                    for person_box in person_boxes:
                        if is_person_in_task_area(person_box, task_area):
                            target_person = person_box
                            break
                    
                    # 如果沒有人在作業區域內，選擇最接近的
                    if target_person is None:
                        target_person = find_closest_person(person_boxes, task_area)
                else:
                    # 沒有作業區域設定，選擇第一個人
                    target_person = person_boxes[0]
            
            # 處理選定的人員
            if target_person is not None:
                x1, y1, x2, y2 = map(int, target_person)
                person_roi = frame[y1:y2, x1:x2]
                
                # MediaPipe 姿態檢測
                person_roi_rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
                pose_results = pose.process(person_roi_rgb)
                
                if pose_results.pose_landmarks:
                    # 準備CSV行資料
                    row_data = {
                        'frame': frame_count,
                        'label': '未標記'  # 預設標籤
                    }
                    
                    # 添加關鍵點資料
                    for i, landmark in enumerate(pose_results.pose_landmarks.landmark):
                        # 將相對座標轉換為絕對座標
                        abs_x = landmark.x * (x2 - x1) + x1
                        abs_y = landmark.y * (y2 - y1) + y1
                        
                        row_data[f'{i}_x'] = abs_x / frame.shape[1]  # 正規化到 [0,1]
                        row_data[f'{i}_y'] = abs_y / frame.shape[0]  # 正規化到 [0,1]
                        row_data[f'{i}_z'] = landmark.z
                        row_data[f'{i}_visibility'] = landmark.visibility
                    
                    writer.writerow(row_data)
                    processed_frames += 1
            
            # 顯示進度
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"處理進度: {frame_count}/{total_frames} 幀 ({progress:.1f}%) - 已處理: {processed_frames} 幀")
        
        cap.release()
        pose.close()
        
        print(f"\n✅ 處理完成！")
        print(f"總幀數: {frame_count}")
        print(f"成功處理: {processed_frames} 幀")
        print(f"成功率: {(processed_frames/frame_count)*100:.1f}%")
        print(f"輸出檔案: {csv_path}")

if __name__ == "__main__":
    main()
