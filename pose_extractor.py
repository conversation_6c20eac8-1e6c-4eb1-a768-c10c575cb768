import cv2
import csv
import json
import os
import time
import numpy as np
from datetime import datetime
from collections import deque
import logging
from ultralytics import YOLO
import pandas as pd

class PoseExtractor:
    def __init__(self, config_file='pose_config.json'):
        """初始化姿勢提取器"""
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.init_models()
        self.init_mediapipe()
        self.init_tracking()
        
    def load_config(self, config_file):
        """載入配置檔案"""
        default_config = {
            'yolo_model': 'yolov8n.pt',
            'task_area_config': 'task_area_config.json',
            'confidence_threshold': 0.5,
            'output_dir': 'pose_output',
            'log_level': 'INFO',
            'frame_skip': 1,  # 每隔幾幀處理一次
            'pose_confidence_threshold': 0.3,
            'tracking_confidence_threshold': 0.3,
            'enable_pose_smoothing': True,
            'smoothing_window': 5,
            'enable_quality_filter': True,
            'min_pose_visibility': 0.5,
            'export_formats': ['csv', 'json', 'npy']
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"載入配置失敗: {e}")
        
        return default_config
    
    def setup_logging(self):
        """設定日誌"""
        if not os.path.exists(self.config['output_dir']):
            os.makedirs(self.config['output_dir'])
            
        log_file = os.path.join(self.config['output_dir'], 
                               f"pose_extractor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        logging.basicConfig(
            level=getattr(logging, self.config['log_level']),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def init_models(self):
        """初始化模型"""
        try:
            self.yolo_model = YOLO(self.config['yolo_model'])
            # 強制使用 GPU
            import torch
            if torch.cuda.is_available():
                self.yolo_model.to('cuda')
                self.logger.info("✅ YOLO模型載入成功 (GPU)")
            else:
                self.logger.warning("⚠️ YOLO模型載入成功 (CPU) - 未檢測到GPU")
        except Exception as e:
            self.logger.error(f"❌ YOLO模型載入失敗: {e}")
            raise
    
    def init_mediapipe(self):
        """初始化MediaPipe"""
        try:
            import mediapipe as mp
            self.mp_pose = mp.solutions.pose
            self.pose = self.mp_pose.Pose(
                static_image_mode=False,
                model_complexity=2,
                enable_segmentation=False,
                min_detection_confidence=self.config['pose_confidence_threshold'],
                min_tracking_confidence=self.config['tracking_confidence_threshold']
            )
            self.mp_drawing = mp.solutions.drawing_utils
            self.logger.info("✅ MediaPipe初始化成功")
        except ImportError as e:
            self.logger.error(f"❌ MediaPipe導入失敗: {e}")
            raise
    
    def init_tracking(self):
        """初始化追蹤"""
        self.pose_history = deque(maxlen=self.config['smoothing_window'])
        self.stats = {
            'total_frames': 0,
            'processed_frames': 0,
            'detected_poses': 0,
            'quality_filtered': 0,
            'start_time': time.time()
        }
        self.pose_data = []

        # 人員追蹤相關
        self.primary_person = None  # 主要工作人員的邊界框
        self.person_lost_frames = 0  # 人員丟失的連續幀數
        self.max_lost_frames = 10  # 最大允許丟失幀數
        self.tracking_threshold = 0.5  # 追蹤重疊閾值
        
    def load_task_area(self):
        """載入作業區域"""
        config_file = self.config['task_area_config']
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('task_area', None)
            except:
                return None
        return None
    
    def is_person_in_task_area(self, person_box, task_area, overlap_threshold=0.8):
        """判斷人員是否在作業區域內"""
        if task_area is None:
            return True

        px1, py1, px2, py2 = person_box
        tx1, ty1, tx2, ty2 = task_area

        overlap_x1 = max(px1, tx1)
        overlap_y1 = max(py1, ty1)
        overlap_x2 = min(px2, tx2)
        overlap_y2 = min(py2, ty2)

        if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
            return False

        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
        person_area = (px2 - px1) * (py2 - py1)
        overlap_ratio = overlap_area / person_area if person_area > 0 else 0

        return overlap_ratio >= overlap_threshold

    def calculate_box_overlap(self, box1, box2):
        """計算兩個邊界框的重疊比例"""
        if box1 is None or box2 is None:
            return 0.0

        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # 計算重疊區域
        overlap_x1 = max(x1_1, x1_2)
        overlap_y1 = max(y1_1, y1_2)
        overlap_x2 = min(x2_1, x2_2)
        overlap_y2 = min(y2_1, y2_2)

        if overlap_x2 <= overlap_x1 or overlap_y2 <= overlap_y1:
            return 0.0

        # 計算重疊面積
        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)

        # 計算第一個框的面積
        box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)

        if box1_area == 0:
            return 0.0

        return overlap_area / box1_area
    
    def find_best_person(self, frame, task_area):
        """找出最佳人員（帶追蹤功能）"""
        results = self.yolo_model.predict(frame, conf=self.config['confidence_threshold'], verbose=False)
        candidates = []

        if len(results) > 0 and results[0].boxes is not None:
            for box in results[0].boxes:
                if int(box.cls[0]) == 0:  # person class
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0])
                    person_box = [int(x1), int(y1), int(x2), int(y2)]

                    # 計算人員大小和品質分數
                    area = (x2 - x1) * (y2 - y1)
                    aspect_ratio = (y2 - y1) / (x2 - x1) if (x2 - x1) > 0 else 0

                    # 品質分數 = 信心度 * 面積權重 * 長寬比權重
                    quality_score = confidence * min(area / 10000, 1.0) * min(aspect_ratio / 2.0, 1.0)

                    candidates.append({
                        'box': person_box,
                        'confidence': confidence,
                        'quality_score': quality_score,
                        'in_task_area': self.is_person_in_task_area(person_box, task_area)
                    })

        if not candidates:
            # 沒有檢測到人員
            self.person_lost_frames += 1
            if self.person_lost_frames > self.max_lost_frames:
                # 人員丟失太久，重置追蹤
                self.primary_person = None
                self.person_lost_frames = 0
                self.logger.warning("🔄 主要工作人員丟失，重置追蹤")
            return None

        # 如果已有主要工作人員，優先追蹤該人員
        if self.primary_person is not None:
            # 尋找與當前追蹤人員最相似的候選者
            best_match = None
            best_overlap = 0

            for candidate in candidates:
                overlap = self.calculate_box_overlap(self.primary_person, candidate['box'])
                if overlap > best_overlap and overlap >= self.tracking_threshold:
                    best_overlap = overlap
                    best_match = candidate

            if best_match is not None:
                # 找到匹配的人員，繼續追蹤
                self.person_lost_frames = 0
                self.primary_person = best_match['box']
                self.logger.debug(f"🎯 繼續追蹤主要工作人員 (重疊度: {best_overlap:.2f})")
                return best_match['box']
            else:
                # 沒有找到匹配的人員
                self.person_lost_frames += 1
                if self.person_lost_frames > self.max_lost_frames:
                    # 人員丟失太久，重新選擇
                    self.primary_person = None
                    self.person_lost_frames = 0
                    self.logger.warning("🔄 主要工作人員丟失，重新選擇")
                else:
                    # 暫時保持追蹤
                    return self.primary_person

        # 沒有主要工作人員或需要重新選擇，選擇最佳候選者
        # **只選擇在作業區域內的人員**
        if task_area is not None:
            in_area_candidates = [c for c in candidates if c['in_task_area']]
            if in_area_candidates:
                best_candidate = max(in_area_candidates, key=lambda c: c['quality_score'])
                self.primary_person = best_candidate['box']
                self.person_lost_frames = 0
                self.logger.info("🎯 選定新的主要工作人員（作業區域內）")
                return best_candidate['box']
            else:
                # 沒有人在作業區域內，不選擇任何人
                self.logger.warning("⚠️ 沒有人員在指定作業區域內")
                return None

        # 如果沒有設定作業區域，選擇最高品質的人員
        best_candidate = max(candidates, key=lambda c: c['quality_score'])
        self.primary_person = best_candidate['box']
        self.person_lost_frames = 0
        self.logger.info("🎯 選定新的主要工作人員（最高品質）")
        return best_candidate['box']
    
    def extract_pose_landmarks(self, frame, person_box):
        """提取姿勢關鍵點"""
        x1, y1, x2, y2 = person_box
        person_roi = frame[y1:y2, x1:x2]
        
        if person_roi.size == 0:
            return None
        
        # 將ROI轉換為RGB
        person_roi_rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
        
        # 使用MediaPipe進行姿勢估計
        results = self.pose.process(person_roi_rgb)
        
        if results.pose_landmarks:
            # 將關鍵點座標轉換回原始圖像座標系
            landmarks = []
            for landmark in results.pose_landmarks.landmark:
                abs_x = int(landmark.x * (x2 - x1) + x1)
                abs_y = int(landmark.y * (y2 - y1) + y1)
                landmarks.append({
                    'x': abs_x,
                    'y': abs_y,
                    'z': landmark.z,
                    'visibility': landmark.visibility
                })
            
            return {
                'landmarks': landmarks,
                'pose_world_landmarks': results.pose_world_landmarks,
                'visibility_score': sum(lm['visibility'] for lm in landmarks) / len(landmarks)
            }
        
        return None
    
    def smooth_pose_data(self, pose_data):
        """平滑姿勢數據"""
        if not self.config['enable_pose_smoothing']:
            return pose_data
        
        self.pose_history.append(pose_data)
        
        if len(self.pose_history) < 2:
            return pose_data
        
        # 計算平均值進行平滑
        smoothed_landmarks = []
        for i in range(len(pose_data['landmarks'])):
            avg_x = sum(h['landmarks'][i]['x'] for h in self.pose_history) / len(self.pose_history)
            avg_y = sum(h['landmarks'][i]['y'] for h in self.pose_history) / len(self.pose_history)
            avg_z = sum(h['landmarks'][i]['z'] for h in self.pose_history) / len(self.pose_history)
            avg_visibility = sum(h['landmarks'][i]['visibility'] for h in self.pose_history) / len(self.pose_history)
            
            smoothed_landmarks.append({
                'x': int(avg_x),
                'y': int(avg_y),
                'z': avg_z,
                'visibility': avg_visibility
            })
        
        return {
            'landmarks': smoothed_landmarks,
            'pose_world_landmarks': pose_data['pose_world_landmarks'],
            'visibility_score': pose_data['visibility_score']
        }
    
    def is_high_quality_pose(self, pose_data):
        """判斷姿勢品質是否足夠好"""
        if not self.config['enable_quality_filter']:
            return True
        
        return pose_data['visibility_score'] >= self.config['min_pose_visibility']
    
    def draw_pose_on_frame(self, frame, pose_data, person_box):
        """在幀上繪製姿勢"""
        if pose_data is None:
            return frame
        
        # 繪製人員邊界框
        x1, y1, x2, y2 = person_box
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # 繪製姿勢關鍵點
        for i, landmark in enumerate(pose_data['landmarks']):
            if landmark['visibility'] > 0.3:
                cv2.circle(frame, (landmark['x'], landmark['y']), 5, (0, 0, 255), -1)
                cv2.putText(frame, str(i), (landmark['x'], landmark['y']), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 繪製連接線
        pose_connections = self.mp_pose.POSE_CONNECTIONS
        for connection in pose_connections:
            start_idx, end_idx = connection
            if (start_idx < len(pose_data['landmarks']) and 
                end_idx < len(pose_data['landmarks']) and
                pose_data['landmarks'][start_idx]['visibility'] > 0.3 and
                pose_data['landmarks'][end_idx]['visibility'] > 0.3):
                
                start_point = (pose_data['landmarks'][start_idx]['x'], 
                              pose_data['landmarks'][start_idx]['y'])
                end_point = (pose_data['landmarks'][end_idx]['x'], 
                            pose_data['landmarks'][end_idx]['y'])
                cv2.line(frame, start_point, end_point, (255, 0, 0), 2)
        
        return frame
    
    def process_frame(self, frame, frame_idx, task_area):
        """處理單幀"""
        self.stats['total_frames'] += 1
        
        # 跳幀處理
        if frame_idx % self.config['frame_skip'] != 0:
            return frame, None
        
        self.stats['processed_frames'] += 1
        
        # 找出最佳人員
        person_box = self.find_best_person(frame, task_area)
        if person_box is None:
            return frame, None
        
        # 提取姿勢關鍵點
        pose_data = self.extract_pose_landmarks(frame, person_box)
        if pose_data is None:
            return frame, None
        
        self.stats['detected_poses'] += 1
        
        # 平滑姿勢數據
        pose_data = self.smooth_pose_data(pose_data)
        
        # 品質檢查
        if not self.is_high_quality_pose(pose_data):
            self.stats['quality_filtered'] += 1
            return frame, None
        
        # 保存姿勢數據
        frame_data = {
            'frame_idx': frame_idx,
            'timestamp': time.time(),
            'person_box': person_box,
            'pose_data': pose_data,
            'visibility_score': pose_data['visibility_score']
        }
        self.pose_data.append(frame_data)
        
        # 繪製姿勢
        frame = self.draw_pose_on_frame(frame, pose_data, person_box)
        
        return frame, frame_data
    
    def export_data(self, output_prefix):
        """匯出數據"""
        if not self.pose_data:
            self.logger.warning("沒有姿勢數據可以匯出")
            return
        
        # 匯出CSV
        if 'csv' in self.config['export_formats']:
            self.export_to_csv(output_prefix)
        
        # 匯出JSON
        if 'json' in self.config['export_formats']:
            self.export_to_json(output_prefix)
        
        # 匯出NumPy
        if 'npy' in self.config['export_formats']:
            self.export_to_numpy(output_prefix)
    
    def export_to_csv(self, output_prefix):
        """匯出到CSV"""
        csv_file = os.path.join(self.config['output_dir'], f"{output_prefix}_pose_data.csv")
        
        rows = []
        for frame_data in self.pose_data:
            base_row = {
                'frame_idx': frame_data['frame_idx'],
                'timestamp': frame_data['timestamp'],
                'person_box_x1': frame_data['person_box'][0],
                'person_box_y1': frame_data['person_box'][1],
                'person_box_x2': frame_data['person_box'][2],
                'person_box_y2': frame_data['person_box'][3],
                'visibility_score': frame_data['visibility_score']
            }
            
            # 添加每個關鍵點的座標
            for i, landmark in enumerate(frame_data['pose_data']['landmarks']):
                base_row[f'landmark_{i}_x'] = landmark['x']
                base_row[f'landmark_{i}_y'] = landmark['y']
                base_row[f'landmark_{i}_z'] = landmark['z']
                base_row[f'landmark_{i}_visibility'] = landmark['visibility']
            
            rows.append(base_row)
        
        df = pd.DataFrame(rows)
        df.to_csv(csv_file, index=False, encoding='utf-8')
        self.logger.info(f"✅ CSV數據已匯出至: {csv_file}")
    
    def export_to_json(self, output_prefix):
        """匯出到JSON"""
        json_file = os.path.join(self.config['output_dir'], f"{output_prefix}_pose_data.json")

        # 轉換數據為可序列化格式
        serializable_data = []
        for frame_data in self.pose_data:
            serializable_frame = {
                'frame': frame_data['frame'],
                'timestamp': frame_data['timestamp'],
                'landmarks': frame_data['landmarks'],  # 這已經是字典格式
                'visibility_score': frame_data['visibility_score']
            }
            serializable_data.append(serializable_frame)

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)

        self.logger.info(f"✅ JSON數據已匯出至: {json_file}")
    
    def export_to_numpy(self, output_prefix):
        """匯出到NumPy"""
        npy_file = os.path.join(self.config['output_dir'], f"{output_prefix}_pose_data.npy")
        
        # 將姿勢數據轉換為NumPy數組
        pose_arrays = []
        for frame_data in self.pose_data:
            frame_array = []
            for landmark in frame_data['pose_data']['landmarks']:
                frame_array.extend([landmark['x'], landmark['y'], landmark['z'], landmark['visibility']])
            pose_arrays.append(frame_array)
        
        np.save(npy_file, np.array(pose_arrays))
        self.logger.info(f"✅ NumPy數據已匯出至: {npy_file}")
    
    def print_stats(self):
        """打印統計信息"""
        elapsed_time = time.time() - self.stats['start_time']
        fps = self.stats['processed_frames'] / elapsed_time if elapsed_time > 0 else 0
        
        print("\n" + "="*50)
        print("🎯 姿勢提取統計")
        print("="*50)
        print(f"📊 總幀數: {self.stats['total_frames']}")
        print(f"⚡ 處理幀數: {self.stats['processed_frames']}")
        print(f"🎪 檢測到姿勢: {self.stats['detected_poses']}")
        print(f"🚫 品質過濾: {self.stats['quality_filtered']}")
        print(f"⏱️ 處理時間: {elapsed_time:.2f}秒")
        print(f"📈 處理速度: {fps:.2f} FPS")
        print("="*50)
    
    def process_video(self, input_path, output_path=None):
        """處理視頻"""
        self.logger.info(f"🎬 開始處理視頻: {input_path}")
        
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            self.logger.error(f"❌ 無法打開視頻: {input_path}")
            return
        
        # 獲取視頻屬性
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        self.logger.info(f"📹 視頻屬性: {width}x{height}, {fps}FPS, {total_frames}幀")
        
        # 設置輸出視頻
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        # 載入作業區域
        task_area = self.load_task_area()
        if task_area:
            self.logger.info(f"📍 作業區域: {task_area}")
        
        frame_idx = 0
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 處理幀
                processed_frame, frame_data = self.process_frame(frame, frame_idx, task_area)
                
                # 寫入輸出視頻
                if output_path:
                    out.write(processed_frame)
                
                # 顯示進度
                if frame_idx % 100 == 0:
                    progress = (frame_idx / total_frames) * 100
                    self.logger.info(f"📊 處理進度: {progress:.1f}% ({frame_idx}/{total_frames})")
                
                frame_idx += 1
        
        except KeyboardInterrupt:
            self.logger.info("⏹️ 用戶中斷處理")
        
        finally:
            cap.release()
            if output_path:
                out.release()
            
            # 匯出數據
            output_prefix = os.path.splitext(os.path.basename(input_path))[0]
            self.export_data(output_prefix)
            
            # 打印統計信息
            self.print_stats()
            
            self.logger.info("✅ 視頻處理完成")

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='姿勢提取器')
    parser.add_argument('input_video', help='輸入視頻檔案')
    parser.add_argument('--output', '-o', help='輸出視頻檔案')
    parser.add_argument('--config', '-c', default='pose_config.json', help='配置檔案')
    
    args = parser.parse_args()
    
    # 創建姿勢提取器
    extractor = PoseExtractor(args.config)
    
    # 處理視頻
    extractor.process_video(args.input_video, args.output)

if __name__ == "__main__":
    main()