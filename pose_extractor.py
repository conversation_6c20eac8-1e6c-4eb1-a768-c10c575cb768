
import cv2
import mediapipe as mp
import csv
import json
import os
from ultralytics import YOLO

# 初始化
mp_pose = mp.solutions.pose
# 改進的 MediaPipe 設定，提高對彎腰等困難姿勢的偵測能力
pose = mp_pose.Pose(
    static_image_mode=False,
    model_complexity=2,
    enable_segmentation=False,
    min_detection_confidence=0.3,
    min_tracking_confidence=0.3
)
model = YOLO('yolov8n.pt')

video_path = '1.mp4'
csv_path = 'pose_data.csv'
config_file = 'task_area_config.json'

def load_task_area():
    """載入作業區域設定"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def is_person_in_task_area(person_box, task_area, overlap_threshold=0.3):
    """判斷人員是否在作業區域內"""
    if task_area is None:
        return True

    px1, py1, px2, py2 = person_box
    tx1, ty1, tx2, ty2 = task_area

    # 計算重疊區域
    overlap_x1 = max(px1, tx1)
    overlap_y1 = max(py1, ty1)
    overlap_x2 = min(px2, tx2)
    overlap_y2 = min(py2, ty2)

    if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
        return False

    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
    person_area = (px2 - px1) * (py2 - py1)
    overlap_ratio = overlap_area / person_area if person_area > 0 else 0

    return overlap_ratio >= overlap_threshold

def find_main_worker(frame, task_area):
    """找出主要作業員工 - 嚴格版本"""
    results = model.predict(frame, conf=0.5, verbose=False)
    people = []

    if len(results) > 0 and results[0].boxes is not None:
        for box in results[0].boxes:
            if int(box.cls[0]) == 0:  # person class
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0])
                person_box = [int(x1), int(y1), int(x2), int(y2)]
                people.append({
                    'box': person_box,
                    'confidence': confidence,
                    'in_task_area': is_person_in_task_area(person_box, task_area)
                })

    if not people:
        return None

    if task_area is None:
        best_person = max(people, key=lambda p: p['confidence'])
        return best_person['box']

    # 只考慮在作業區域內的人員
    people_in_area = [p for p in people if p['in_task_area']]

    if people_in_area:
        best_person = max(people_in_area, key=lambda p: p['confidence'])
        return best_person['box']
    else:
        # 沒有人在作業區域內，返回None
        return None

def extract_person_roi_advanced(frame, person_box):
    """改進的人員ROI提取，嘗試多種策略"""
    if person_box is None:
        return None, None

    h, w = frame.shape[:2]
    x1, y1, x2, y2 = person_box

    # 嘗試多種 padding 策略
    strategies = [30, 50, 10]

    for padding in strategies:
        roi_x1 = max(0, x1 - padding)
        roi_y1 = max(0, y1 - padding)
        roi_x2 = min(w, x2 + padding)
        roi_y2 = min(h, y2 + padding)

        person_roi = frame[roi_y1:roi_y2, roi_x1:roi_x2]

        # 確保 ROI 有足夠大小
        if person_roi.shape[0] >= 50 and person_roi.shape[1] >= 50:
            # 嘗試姿勢偵測
            rgb_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
            results = pose.process(rgb_roi)

            if results.pose_landmarks:
                return person_roi, results.pose_landmarks

    # 如果所有 ROI 策略都失敗，嘗試全畫面
    rgb_full = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = pose.process(rgb_full)

    if results.pose_landmarks:
        # 檢查姿勢是否在人員附近
        person_center = ((x1 + x2) / 2, (y1 + y2) / 2)
        landmarks = results.pose_landmarks.landmark
        pose_x = sum([lm.x for lm in landmarks]) / len(landmarks) * w
        pose_y = sum([lm.y for lm in landmarks]) / len(landmarks) * h
        pose_center = (pose_x, pose_y)

        # 計算距離
        dist = ((person_center[0] - pose_center[0]) ** 2 + (person_center[1] - pose_center[1]) ** 2) ** 0.5

        if dist < 100:  # 如果姿勢中心在人員附近
            return frame, results.pose_landmarks

    return None, None

# 載入作業區域設定
task_area = load_task_area()
if task_area:
    print(f"✅ 載入作業區域設定: {task_area}")
    print("🎯 將只分析主要作業員工的姿勢")
else:
    print("⚠️ 未找到作業區域設定，將分析第一個偵測到的人員")
    print("💡 建議先運行 yolo_pose_gui.py 設定作業區域")

cap = cv2.VideoCapture(video_path)
fps = int(cap.get(cv2.CAP_PROP_FPS))

with open(csv_path, mode='w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['frame', 'label'] + [f'{i}_{c}' for i in range(33) for c in ['x', 'y', 'z', 'visibility']])

    frame_id = 0
    processed_frames = 0

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break

        # 找出主要作業員工
        main_worker_box = find_main_worker(frame, task_area)

        if main_worker_box is not None:
            # 使用改進的ROI提取和姿勢偵測
            person_roi, pose_landmarks = extract_person_roi_advanced(frame, main_worker_box)

            if pose_landmarks is not None:
                row = [frame_id, '']  # Label 手動填寫
                for lm in pose_landmarks.landmark:
                    row += [lm.x, lm.y, lm.z, lm.visibility]
                writer.writerow(row)
                processed_frames += 1

        frame_id += 1

        # 顯示進度
        if frame_id % 30 == 0:  # 每30幀顯示一次
            print(f"處理進度: {frame_id} 幀, 成功分析: {processed_frames} 幀")

cap.release()
print(f"✅ 姿勢資料已儲存到 {csv_path}")
print(f"📊 總共處理 {frame_id} 幀，成功分析 {processed_frames} 幀")
print(f"📈 成功率: {processed_frames/frame_id*100:.1f}%")
