
import cv2
import csv
import json
import os
from ultralytics import YOLO

# 延遲導入 MediaPipe 以避免 TensorFlow 依賴問題
def get_mediapipe():
    try:
        import mediapipe as mp
        return mp
    except ImportError as e:
        print(f"MediaPipe 導入失敗: {e}")
        return None

# 初始化 YOLO
model = YOLO('yolov8n.pt')

video_path = '2.mp4'
csv_path = 'pose_data.csv'
config_file = 'task_area_config.json'

def load_task_area():
    """載入作業區域設定"""
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def is_person_in_task_area(person_box, task_area, overlap_threshold=0.3):
    """判斷人員是否在作業區域內"""
    if task_area is None:
        return True

    px1, py1, px2, py2 = person_box
    tx1, ty1, tx2, ty2 = task_area

    # 計算重疊區域
    overlap_x1 = max(px1, tx1)
    overlap_y1 = max(py1, ty1)
    overlap_x2 = min(px2, tx2)
    overlap_y2 = min(py2, ty2)

    if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
        return False

    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
    person_area = (px2 - px1) * (py2 - py1)
    overlap_ratio = overlap_area / person_area if person_area > 0 else 0

    return overlap_ratio >= overlap_threshold

# 移除了有問題的函數，邏輯已整合到主函數中

def main():
    """主函數"""
    print("開始處理影片...")
    print(f"影片路徑: {video_path}")
    print(f"輸出CSV: {csv_path}")

    # 初始化 MediaPipe
    mp = get_mediapipe()
    if mp is None:
        print("❌ MediaPipe 初始化失敗")
        return

    mp_pose = mp.solutions.pose
    pose = mp_pose.Pose(
        static_image_mode=False,
        model_complexity=2,
        enable_segmentation=False,
        min_detection_confidence=0.3,
        min_tracking_confidence=0.3
    )

    # 載入作業區域設定
    task_area = load_task_area()
    if task_area:
        print(f"✅ 載入作業區域設定: {task_area}")
        print("🎯 將只分析主要作業員工的姿勢")
    else:
        print("⚠️ 未找到作業區域設定，將分析第一個偵測到的人員")
        print("💡 建議先運行 yolo_pose_gui.py 設定作業區域")

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ 無法開啟影片: {video_path}")
        return

    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"影片資訊: {total_frames} 幀, {fps} FPS")

    with open(csv_path, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['frame', 'label'] + [f'{i}_{c}' for i in range(33) for c in ['x', 'y', 'z', 'visibility']])

        frame_id = 0
        processed_frames = 0

        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break

            frame_id += 1

            # YOLO 人員檢測
            results = model(frame, verbose=False)

            person_boxes = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        if int(box.cls[0]) == 0:  # 人員類別
                            person_box = box.xyxy[0].cpu().numpy()
                            person_boxes.append(person_box)

            # 找到最適合的人員
            target_person = None
            if person_boxes:
                if task_area:
                    # 優先選擇在作業區域內的人員
                    for person_box in person_boxes:
                        if is_person_in_task_area(person_box, task_area):
                            target_person = person_box
                            break

                    # 如果沒有人在作業區域內，選擇最接近的
                    if target_person is None:
                        task_center = center(task_area)
                        min_distance = float('inf')
                        for person_box in person_boxes:
                            person_center = center(person_box)
                            dist = distance(person_center, task_center)
                            if dist < min_distance:
                                min_distance = dist
                                target_person = person_box
                else:
                    # 沒有作業區域設定，選擇第一個人
                    target_person = person_boxes[0]

            # 處理選定的人員
            if target_person is not None:
                x1, y1, x2, y2 = map(int, target_person)
                person_roi = frame[y1:y2, x1:x2]

                # MediaPipe 姿態檢測
                person_roi_rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
                pose_results = pose.process(person_roi_rgb)

                if pose_results.pose_landmarks:
                    row = [frame_id, '']  # Label 手動填寫
                    for lm in pose_results.pose_landmarks.landmark:
                        # 將相對座標轉換為絕對座標
                        abs_x = lm.x * (x2 - x1) + x1
                        abs_y = lm.y * (y2 - y1) + y1

                        # 正規化到 [0,1]
                        norm_x = abs_x / frame.shape[1]
                        norm_y = abs_y / frame.shape[0]

                        row += [norm_x, norm_y, lm.z, lm.visibility]
                    writer.writerow(row)
                    processed_frames += 1

            # 顯示進度
            if frame_id % 30 == 0:  # 每30幀顯示一次
                progress = (frame_id / total_frames) * 100
                print(f"處理進度: {frame_id}/{total_frames} 幀 ({progress:.1f}%), 成功分析: {processed_frames} 幀")

    cap.release()
    pose.close()
    print(f"✅ 姿勢資料已儲存到 {csv_path}")
    print(f"📊 總共處理 {frame_id} 幀，成功分析 {processed_frames} 幀")
    print(f"📈 成功率: {processed_frames/frame_id*100:.1f}%")

if __name__ == "__main__":
    main()
