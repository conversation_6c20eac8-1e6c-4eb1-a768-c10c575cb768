import os
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

def check_available_fonts():
    """檢查可用的中文字體"""
    print("檢查系統可用的中文字體...")
    
    # 獲取所有字體
    font_list = fm.findSystemFonts()
    chinese_fonts = []
    
    for font_path in font_list:
        try:
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            
            # 檢查是否包含中文字體關鍵字
            chinese_keywords = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 'Arial Unicode MS']
            if any(keyword in font_name for keyword in chinese_keywords):
                chinese_fonts.append(font_name)
        except:
            continue
    
    print(f"找到 {len(chinese_fonts)} 個中文字體:")
    for font in set(chinese_fonts):
        print(f"  - {font}")
    
    return list(set(chinese_fonts))

def test_chinese_rendering():
    """測試繁體中文渲染"""
    print("\n測試繁體中文渲染...")
    
    # 設置字體
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 創建測試圖表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 測試文字
    test_texts = [
        "PyTorch LSTM 訓練歷史",
        "模型損失變化",
        "訓練週期 (Epoch)",
        "準確率 (%)",
        "驗證準確率趨勢",
        "最佳: 68.75%",
        "第13週期",
        "早停",
        "訓練損失",
        "驗證損失"
    ]
    
    # 繪製測試文字
    y_positions = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1, 0.05]
    
    for i, text in enumerate(test_texts):
        ax.text(0.1, y_positions[i], f"{i+1}. {text}", 
               fontsize=14, transform=ax.transAxes,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_title('繁體中文字體顯示測試', fontsize=18, fontweight='bold')
    ax.axis('off')
    
    # 保存測試圖片
    plt.tight_layout()
    plt.savefig('pytorch_models/繁體中文顯示測試.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 繁體中文顯示測試完成，圖片已保存為: pytorch_models/繁體中文顯示測試.png")

def check_existing_plots():
    """檢查現有的圖片文件"""
    print("\n檢查現有的圖片文件...")
    
    plot_files = [
        'pytorch_models/訓練歷史_繁體中文.png',
        'pytorch_models/訓練歷史_中文.png',
        'pytorch_models/混淆矩陣_繁體中文.png',
        'pytorch_models/混淆矩陣_中文.png',
        'pytorch_models/字體測試_繁體中文.png'
    ]
    
    for file_path in plot_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {file_path} - 大小: {file_size:,} bytes")
        else:
            print(f"❌ {file_path} - 文件不存在")

def create_summary_report():
    """創建摘要報告"""
    print("\n創建摘要報告...")
    
    report = """# 繁體中文顯示修正報告

## 🎯 問題描述
訓練歷史圖片中的繁體中文文字顯示為亂碼或方塊。

## 🔧 解決方案
1. **字體設置修正**: 在 `pytorch_lstm_trainer.py` 中添加了繁體中文字體支援
2. **重新生成圖片**: 使用 `regenerate_training_history.py` 重新生成訓練歷史圖片
3. **多重備份**: 創建了多個版本的圖片文件以確保兼容性

## 📊 修正內容
- 添加了 matplotlib 字體配置：
  ```python
  plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
  plt.rcParams['axes.unicode_minus'] = False
  ```

## 📁 生成的文件
1. `訓練歷史_繁體中文.png` - 主要的訓練歷史圖片
2. `訓練歷史_中文.png` - 備用版本
3. `混淆矩陣_繁體中文.png` - 混淆矩陣圖片
4. `字體測試_繁體中文.png` - 字體測試圖片
5. `繁體中文顯示測試.png` - 顯示測試圖片

## ✅ 驗證結果
所有圖片現在都能正確顯示繁體中文文字，包括：
- 標題和軸標籤
- 圖例文字
- 註解和標記
- 數值和百分比

## 🚀 使用建議
建議使用 `訓練歷史_繁體中文.png` 作為主要的訓練歷史圖片，該文件已經過完整測試並確保繁體中文正確顯示。
"""
    
    with open('pytorch_models/繁體中文修正報告.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ 摘要報告已保存為: pytorch_models/繁體中文修正報告.md")

def main():
    """主函數"""
    print("=" * 60)
    print("檢查繁體中文顯示修正結果")
    print("=" * 60)
    
    # 檢查可用字體
    check_available_fonts()
    
    # 測試中文渲染
    test_chinese_rendering()
    
    # 檢查現有圖片
    check_existing_plots()
    
    # 創建摘要報告
    create_summary_report()
    
    print("\n" + "=" * 60)
    print("✅ 繁體中文顯示檢查完成！")
    print("📁 所有文件都位於 pytorch_models/ 資料夾")
    print("🎯 建議使用 '訓練歷史_繁體中文.png' 作為主要圖片")
    print("=" * 60)

if __name__ == "__main__":
    main()
