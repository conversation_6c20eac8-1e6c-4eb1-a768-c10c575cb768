# 增強型 SOP LSTM 模型報告

## 模型信息
- **時間戳記**: 2025-07-11 14:25:50
- **模型類型**: Enhanced SOP LSTM with Attention
- **框架**: PyTorch
- **設備**: cuda

## 數據信息
- **數據文件**: sop_features_已標記_繁體中文.csv
- **序列長度**: 15
- **特徵數量**: N/A
- **類別數量**: 19
- **類別名稱**: 待機, 旋轉工件(順時針), 結束動作, 鎖第10顆螺絲, 鎖第11顆螺絲, 鎖第12顆螺絲, 鎖第13顆螺絲, 鎖第14顆螺絲, 鎖第15顆螺絲, 鎖第16顆螺絲, 鎖第1顆螺絲, 鎖第2顆螺絲, 鎖第3顆螺絲, 鎖第4顆螺絲, 鎖第5顆螺絲, 鎖第6顆螺絲, 鎖第7顆螺絲, 鎖第8顆螺絲, 鎖第9顆螺絲

## 模型架構
- **隱藏層大小**: 128
- **LSTM 層數**: 2
- **Dropout 比率**: 0.3
- **雙向 LSTM**: True
- **注意力頭數**: 4
- **總參數數量**: 867,219

## 訓練配置
- **批次大小**: 32
- **學習率**: 0.001
- **權重衰減**: 0.0001
- **最大訓練輪次**: 100
- **早停耐心**: 15
- **數據增強**: True
- **類別權重**: True

## 性能指標
- **準確率**: 0.9964
- **F1 分數**: 0.9965

## 文件輸出
- 模型文件: `enhanced_sop_lstm_model.pth`
- 混淆矩陣: `混淆矩陣.png`
- 訓練歷史: `訓練歷史.png`
- 特徵重要性: `特徵重要性.png`
- 詳細報告: `detailed_report.json`
