import cv2
import mediapipe as mp
import numpy as np
import torch
import torch.nn as nn
import joblib
import json
import os
import pandas as pd
from ultralytics import YOLO
from collections import deque
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 設置繁體中文字體支援
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 設置設備
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用設備: {device}")

class SOPLSTM(nn.Module):
    """SOP LSTM模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes, dropout=0.2):
        super(SOPLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM層
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )
        
        # 全連接層
        self.fc1 = nn.Linear(hidden_size, 64)
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(64, num_classes)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # LSTM層
        lstm_out, _ = self.lstm(x)
        # 取最後一個時間步的輸出
        out = lstm_out[:, -1, :]
        # 全連接層
        out = self.fc1(out)
        out = self.relu(out)
        out = self.dropout(out)
        out = self.fc2(out)
        return out

class SOPAnomalyDetector:
    """SOP 異常行為檢測器"""
    def __init__(self, config: Dict):
        self.config = config
        self.action_sequences = deque(maxlen=config.get('sequence_length', 50))
        self.time_thresholds = config.get('time_thresholds', {})
        self.quality_thresholds = config.get('quality_thresholds', {})
        self.reset_stats()
        
    def reset_stats(self):
        """重置統計數據"""
        self.step_times = {}
        self.step_counts = {}
        self.anomaly_records = []
        self.current_step_start = None
        self.last_step = None
        
    def update_sequence(self, step: str, confidence: float, timestamp: float):
        """更新動作序列"""
        self.action_sequences.append({
            'step': step,
            'confidence': confidence,
            'timestamp': timestamp
        })
        
    def check_step_sequence(self) -> List[Dict]:
        """檢查步驟順序異常"""
        anomalies = []
        if len(self.action_sequences) < 2:
            return anomalies
            
        # 定義正確的步驟順序
        correct_sequence = self.config.get('correct_sequence', [])
        if not correct_sequence:
            return anomalies
            
        # 檢查最近的步驟序列
        recent_steps = [item['step'] for item in list(self.action_sequences)[-5:]]
        
        # 檢查是否有步驟跳過
        for i in range(len(recent_steps) - 1):
            current_step = recent_steps[i]
            next_step = recent_steps[i + 1]
            
            if current_step in correct_sequence and next_step in correct_sequence:
                current_idx = correct_sequence.index(current_step)
                next_idx = correct_sequence.index(next_step)
                
                # 檢查是否跳過了步驟
                if next_idx - current_idx > 1:
                    anomalies.append({
                        'type': 'sequence_violation',
                        'description': f'步驟跳過: 從 {current_step} 直接跳到 {next_step}',
                        'severity': 'medium',
                        'timestamp': self.action_sequences[-1]['timestamp']
                    })
                    
        return anomalies
        
    def check_time_anomalies(self, current_step: str, current_time: float) -> List[Dict]:
        """檢查時間異常"""
        anomalies = []
        
        # 檢查步驟執行時間
        if self.last_step and self.current_step_start:
            step_duration = current_time - self.current_step_start
            
            if self.last_step in self.time_thresholds:
                min_time = self.time_thresholds[self.last_step].get('min', 0)
                max_time = self.time_thresholds[self.last_step].get('max', float('inf'))
                
                if step_duration < min_time:
                    anomalies.append({
                        'type': 'time_too_short',
                        'description': f'{self.last_step} 執行時間過短: {step_duration:.1f}s (最小: {min_time}s)',
                        'severity': 'medium',
                        'timestamp': current_time
                    })
                elif step_duration > max_time:
                    anomalies.append({
                        'type': 'time_too_long',
                        'description': f'{self.last_step} 執行時間過長: {step_duration:.1f}s (最大: {max_time}s)',
                        'severity': 'high',
                        'timestamp': current_time
                    })
                    
        # 更新步驟時間記錄
        if current_step != self.last_step:
            self.last_step = current_step
            self.current_step_start = current_time
            
        return anomalies
        
    def check_quality_anomalies(self, step: str, confidence: float) -> List[Dict]:
        """檢查品質異常"""
        anomalies = []
        
        min_confidence = self.quality_thresholds.get(step, {}).get('min_confidence', 0.5)
        
        if confidence < min_confidence:
            anomalies.append({
                'type': 'low_confidence',
                'description': f'{step} 信心度過低: {confidence:.2f} (最小: {min_confidence:.2f})',
                'severity': 'medium',
                'timestamp': self.action_sequences[-1]['timestamp'] if self.action_sequences else 0
            })
            
        return anomalies
        
    def detect_anomalies(self, step: str, confidence: float, timestamp: float) -> List[Dict]:
        """檢測所有異常"""
        self.update_sequence(step, confidence, timestamp)
        
        anomalies = []
        anomalies.extend(self.check_step_sequence())
        anomalies.extend(self.check_time_anomalies(step, timestamp))
        anomalies.extend(self.check_quality_anomalies(step, confidence))
        
        # 記錄異常
        for anomaly in anomalies:
            self.anomaly_records.append(anomaly)
            
        return anomalies

class SOPStatistics:
    """SOP 統計分析器"""
    def __init__(self):
        self.reset_stats()
        
    def reset_stats(self):
        """重置統計數據"""
        self.step_counts = {}
        self.step_durations = {}
        self.confidence_scores = {}
        self.anomaly_counts = {}
        self.total_time = 0
        self.start_time = None
        self.end_time = None
        
    def update_stats(self, step: str, confidence: float, duration: float = None):
        """更新統計數據"""
        # 更新步驟計數
        self.step_counts[step] = self.step_counts.get(step, 0) + 1
        
        # 更新信心度記錄
        if step not in self.confidence_scores:
            self.confidence_scores[step] = []
        self.confidence_scores[step].append(confidence)
        
        # 更新持續時間
        if duration is not None:
            if step not in self.step_durations:
                self.step_durations[step] = []
            self.step_durations[step].append(duration)
            
    def update_anomaly_stats(self, anomaly_type: str):
        """更新異常統計"""
        self.anomaly_counts[anomaly_type] = self.anomaly_counts.get(anomaly_type, 0) + 1
        
    def get_performance_metrics(self) -> Dict:
        """獲取性能指標"""
        metrics = {
            'total_steps': sum(self.step_counts.values()),
            'unique_steps': len(self.step_counts),
            'average_confidence': 0,
            'step_efficiency': {},
            'anomaly_rate': 0
        }
        
        # 計算平均信心度
        all_confidences = []
        for confidences in self.confidence_scores.values():
            all_confidences.extend(confidences)
        metrics['average_confidence'] = np.mean(all_confidences) if all_confidences else 0
        
        # 計算步驟效率
        for step, durations in self.step_durations.items():
            if durations:
                metrics['step_efficiency'][step] = {
                    'average_time': np.mean(durations),
                    'min_time': np.min(durations),
                    'max_time': np.max(durations),
                    'std_time': np.std(durations)
                }
                
        # 計算異常率
        total_anomalies = sum(self.anomaly_counts.values())
        metrics['anomaly_rate'] = total_anomalies / metrics['total_steps'] if metrics['total_steps'] > 0 else 0
        
        return metrics

class SOPFeatureExtractor:
    """增強版 SOP 特徵提取器"""
    def __init__(self):
        # 定義重要的關鍵點索引（MediaPipe 姿態關鍵點）
        self.keypoints = {
            'nose': 0,
            'left_shoulder': 11, 'right_shoulder': 12,
            'left_elbow': 13, 'right_elbow': 14,
            'left_wrist': 15, 'right_wrist': 16,
            'left_pinky': 17, 'right_pinky': 18,
            'left_index': 19, 'right_index': 20,
            'left_thumb': 21, 'right_thumb': 22,
            'left_hip': 23, 'right_hip': 24
        }
        
        # 添加特徵品質評估
        self.feature_quality_weights = {
            'pose_stability': 0.3,
            'landmark_visibility': 0.2,
            'movement_consistency': 0.3,
            'temporal_coherence': 0.2
        }

    def extract_coordinates_from_landmarks(self, landmarks):
        """從 MediaPipe landmarks 提取座標"""
        coords = {}
        
        # 提取關鍵點座標
        points_mapping = {
            '鼻子': 'nose',
            '左肩膀': 'left_shoulder', '右肩膀': 'right_shoulder',
            '左手肘': 'left_elbow', '右手肘': 'right_elbow',
            '左手腕': 'left_wrist', '右手腕': 'right_wrist',
            '左手小指': 'left_pinky', '右手小指': 'right_pinky',
            '左手食指': 'left_index', '右手食指': 'right_index',
            '左手拇指': 'left_thumb', '右手拇指': 'right_thumb',
            '左臀部': 'left_hip', '右臀部': 'right_hip'
        }

        for chinese_name, english_name in points_mapping.items():
            if english_name in self.keypoints:
                idx = self.keypoints[english_name]
                landmark = landmarks.landmark[idx]
                coords[chinese_name] = {
                    'x': landmark.x,
                    'y': landmark.y,
                    'z': landmark.z,
                    'visibility': landmark.visibility
                }

        return coords

    def assess_feature_quality(self, coords_sequence: List[Dict]) -> float:
        """評估特徵品質"""
        if not coords_sequence:
            return 0.0
            
        quality_scores = {}
        
        # 1. 姿態穩定性
        pose_stability = self._calculate_pose_stability(coords_sequence)
        quality_scores['pose_stability'] = pose_stability
        
        # 2. 關鍵點可見性
        landmark_visibility = self._calculate_landmark_visibility(coords_sequence)
        quality_scores['landmark_visibility'] = landmark_visibility
        
        # 3. 動作一致性
        movement_consistency = self._calculate_movement_consistency(coords_sequence)
        quality_scores['movement_consistency'] = movement_consistency
        
        # 4. 時間一致性
        temporal_coherence = self._calculate_temporal_coherence(coords_sequence)
        quality_scores['temporal_coherence'] = temporal_coherence
        
        # 計算加權總分
        total_quality = sum(
            quality_scores[key] * self.feature_quality_weights[key]
            for key in quality_scores
        )
        
        return total_quality
        
    def _calculate_pose_stability(self, coords_sequence: List[Dict]) -> float:
        """計算姿態穩定性"""
        if len(coords_sequence) < 2:
            return 0.0
            
        # 計算關鍵點位置的標準差
        stability_scores = []
        key_points = ['左肩膀', '右肩膀', '左手腕', '右手腕']
        
        for point in key_points:
            positions = []
            for coords in coords_sequence:
                if point in coords:
                    pos = np.array([coords[point]['x'], coords[point]['y'], coords[point]['z']])
                    positions.append(pos)
            
            if len(positions) > 1:
                positions = np.array(positions)
                stability = 1 / (1 + np.mean(np.std(positions, axis=0)))
                stability_scores.append(stability)
                
        return np.mean(stability_scores) if stability_scores else 0.0
        
    def _calculate_landmark_visibility(self, coords_sequence: List[Dict]) -> float:
        """計算關鍵點可見性"""
        if not coords_sequence:
            return 0.0
            
        visibility_scores = []
        for coords in coords_sequence:
            frame_visibilities = []
            for point_data in coords.values():
                if 'visibility' in point_data:
                    frame_visibilities.append(point_data['visibility'])
            
            if frame_visibilities:
                visibility_scores.append(np.mean(frame_visibilities))
                
        return np.mean(visibility_scores) if visibility_scores else 0.0
        
    def _calculate_movement_consistency(self, coords_sequence: List[Dict]) -> float:
        """計算動作一致性"""
        if len(coords_sequence) < 3:
            return 0.0
            
        # 計算手部動作的一致性
        hand_points = ['左手腕', '右手腕']
        consistency_scores = []
        
        for point in hand_points:
            velocities = []
            for i in range(1, len(coords_sequence)):
                if point in coords_sequence[i] and point in coords_sequence[i-1]:
                    curr_pos = np.array([
                        coords_sequence[i][point]['x'],
                        coords_sequence[i][point]['y'],
                        coords_sequence[i][point]['z']
                    ])
                    prev_pos = np.array([
                        coords_sequence[i-1][point]['x'],
                        coords_sequence[i-1][point]['y'],
                        coords_sequence[i-1][point]['z']
                    ])
                    velocity = np.linalg.norm(curr_pos - prev_pos)
                    velocities.append(velocity)
            
            if len(velocities) > 1:
                # 一致性 = 1 / (1 + 速度標準差)
                consistency = 1 / (1 + np.std(velocities))
                consistency_scores.append(consistency)
                
        return np.mean(consistency_scores) if consistency_scores else 0.0
        
    def _calculate_temporal_coherence(self, coords_sequence: List[Dict]) -> float:
        """計算時間一致性"""
        if len(coords_sequence) < 2:
            return 0.0
            
        # 檢查序列中是否有異常的跳躍
        coherence_scores = []
        key_points = ['左手腕', '右手腕', '左肩膀', '右肩膀']
        
        for point in key_points:
            position_changes = []
            for i in range(1, len(coords_sequence)):
                if point in coords_sequence[i] and point in coords_sequence[i-1]:
                    curr_pos = np.array([
                        coords_sequence[i][point]['x'],
                        coords_sequence[i][point]['y'],
                        coords_sequence[i][point]['z']
                    ])
                    prev_pos = np.array([
                        coords_sequence[i-1][point]['x'],
                        coords_sequence[i-1][point]['y'],
                        coords_sequence[i-1][point]['z']
                    ])
                    change = np.linalg.norm(curr_pos - prev_pos)
                    position_changes.append(change)
            
            if position_changes:
                # 檢查是否有異常大的變化
                median_change = np.median(position_changes)
                outlier_threshold = median_change * 3
                outliers = [c for c in position_changes if c > outlier_threshold]
                coherence = 1 - (len(outliers) / len(position_changes))
                coherence_scores.append(coherence)
                
        return np.mean(coherence_scores) if coherence_scores else 0.0

    def calculate_distance(self, p1, p2):
        """計算兩點間的3D距離"""
        return np.sqrt((p1['x'] - p2['x'])**2 +
                      (p1['y'] - p2['y'])**2 +
                      (p1['z'] - p2['z'])**2)

    def calculate_angle(self, p1, p2, p3):
        """計算三點形成的角度"""
        # 向量 p2->p1 和 p2->p3
        v1 = np.array([p1['x'] - p2['x'], p1['y'] - p2['y'], p1['z'] - p2['z']])
        v2 = np.array([p3['x'] - p2['x'], p3['y'] - p2['y'], p3['z'] - p2['z']])

        # 計算角度
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi

        return angle

    def extract_sop_features_from_sequence(self, coords_sequence, time_sequence):
        """從座標序列提取增強的 SOP 特徵"""
        if len(coords_sequence) == 0:
            return np.zeros(35)  # 增加特徵數量

        # 使用最新的座標
        coords = coords_sequence[-1]

        # 計算速度序列
        speed_sequences = {'左手腕': [], '右手腕': []}
        for i in range(1, len(coords_sequence)):
            dt = time_sequence[i] - time_sequence[i-1] + 1e-8
            for point in ['左手腕', '右手腕']:
                if point in coords_sequence[i] and point in coords_sequence[i-1]:
                    dx = coords_sequence[i][point]['x'] - coords_sequence[i-1][point]['x']
                    dy = coords_sequence[i][point]['y'] - coords_sequence[i-1][point]['y']
                    dz = coords_sequence[i][point]['z'] - coords_sequence[i-1][point]['z']
                    speed = np.sqrt(dx**2 + dy**2 + dz**2) / dt
                    speed_sequences[point].append(speed)

        features = []

        # 原有特徵（32個）
        # 1. 手部關鍵特徵
        left_elbow_angle = self.calculate_angle(
            coords['左肩膀'], coords['左手肘'], coords['左手腕']
        )
        right_elbow_angle = self.calculate_angle(
            coords['右肩膀'], coords['右手肘'], coords['右手腕']
        )
        features.extend([left_elbow_angle, right_elbow_angle])

        # 2. 手腕角度
        left_wrist_angle = self.calculate_wrist_angle(
            coords['左手腕'], coords['左手肘'], coords['左肩膀']
        )
        right_wrist_angle = self.calculate_wrist_angle(
            coords['右手腕'], coords['右手肘'], coords['右肩膀']
        )
        features.extend([left_wrist_angle, right_wrist_angle])

        # 3. 手臂伸展程度
        left_arm_extension = self.calculate_distance(
            coords['左手腕'], coords['左肩膀']
        )
        right_arm_extension = self.calculate_distance(
            coords['右手腕'], coords['右肩膀']
        )
        features.extend([left_arm_extension, right_arm_extension])

        # 4-32. 其他原有特徵...（為了簡潔，這裡省略了詳細實現）
        # 繼續添加其他特徵直到32個
        
        # 手部動作速度
        left_wrist_speed = speed_sequences['左手腕'][-1] if speed_sequences['左手腕'] else 0.0
        right_wrist_speed = speed_sequences['右手腕'][-1] if speed_sequences['右手腕'] else 0.0
        features.extend([left_wrist_speed, right_wrist_speed])

        # 手部軌跡規律性
        left_wrist_trajectory = [{'x': c['左手腕']['x'], 'y': c['左手腕']['y'], 'z': c['左手腕']['z']}
                                for c in coords_sequence if '左手腕' in c]
        right_wrist_trajectory = [{'x': c['右手腕']['x'], 'y': c['右手腕']['y'], 'z': c['右手腕']['z']}
                                 for c in coords_sequence if '右手腕' in c]

        left_trajectory_regularity = self.calculate_trajectory_regularity(left_wrist_trajectory)
        right_trajectory_regularity = self.calculate_trajectory_regularity(right_wrist_trajectory)
        features.extend([left_trajectory_regularity, right_trajectory_regularity])

        # 雙手協調性
        hands_distance = self.calculate_distance(
            coords['左手腕'], coords['右手腕']
        )
        features.append(hands_distance)

        # 工作姿勢特徵
        shoulder_level = abs(coords['左肩膀']['y'] - coords['右肩膀']['y'])
        features.append(shoulder_level)

        body_center_x = (coords['左肩膀']['x'] + coords['右肩膀']['x']) / 2
        hip_center_x = (coords['左臀部']['x'] + coords['右臀部']['x']) / 2
        body_lean = abs(body_center_x - hip_center_x)
        features.append(body_lean)

        shoulder_center_y = (coords['左肩膀']['y'] + coords['右肩膀']['y']) / 2
        head_position = coords['鼻子']['y'] - shoulder_center_y
        features.append(head_position)

        # 手指精細動作
        left_finger_spread = (
            self.calculate_distance(coords['左手拇指'], coords['左手食指']) +
            self.calculate_distance(coords['左手食指'], coords['左手小指'])
        ) / 2

        right_finger_spread = (
            self.calculate_distance(coords['右手拇指'], coords['右手食指']) +
            self.calculate_distance(coords['右手食指'], coords['右手小指'])
        ) / 2
        features.extend([left_finger_spread, right_finger_spread])

        # 動作穩定性
        left_wrist_stability = np.std(speed_sequences['左手腕']) if len(speed_sequences['左手腕']) > 1 else 0.0
        right_wrist_stability = np.std(speed_sequences['右手腕']) if len(speed_sequences['右手腕']) > 1 else 0.0
        features.extend([left_wrist_stability, right_wrist_stability])

        # 補充特徵到32個
        while len(features) < 32:
            features.append(0.0)

        # 新增特徵品質指標（3個）
        feature_quality = self.assess_feature_quality(coords_sequence)
        pose_stability = self._calculate_pose_stability(coords_sequence)
        landmark_visibility = self._calculate_landmark_visibility(coords_sequence)
        features.extend([feature_quality, pose_stability, landmark_visibility])

        return np.array(features[:35])

    def calculate_wrist_angle(self, wrist, elbow, shoulder):
        """計算手腕旋轉角度"""
        wrist_vector = np.array([wrist['x'] - elbow['x'], wrist['y'] - elbow['y']])
        reference_vector = np.array([shoulder['x'] - elbow['x'], shoulder['y'] - elbow['y']])

        dot_product = np.dot(wrist_vector, reference_vector)
        norms = np.linalg.norm(wrist_vector) * np.linalg.norm(reference_vector)
        cos_angle = dot_product / (norms + 1e-8)

        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi
        return angle

    def calculate_trajectory_regularity(self, coords_history, window_size=10):
        """計算軌跡規律性"""
        if len(coords_history) < window_size:
            return 0.0

        recent_trajectory = coords_history[-window_size:]
        distances = []
        for j in range(1, len(recent_trajectory)):
            dist = np.sqrt(
                (recent_trajectory[j]['x'] - recent_trajectory[j-1]['x'])**2 +
                (recent_trajectory[j]['y'] - recent_trajectory[j-1]['y'])**2 +
                (recent_trajectory[j]['z'] - recent_trajectory[j-1]['z'])**2
            )
            distances.append(dist)

        regularity = 1 / (1 + np.std(distances)) if distances else 0
        return regularity

def load_config():
    """載入配置檔案"""
    config_file = 'sop_config.json'
    default_config = {
        'task_area': None,
        'anomaly_detection': {
            'sequence_length': 50,
            'correct_sequence': ['準備', '定位', '鎖螺絲', '檢查', '完成'],
            'time_thresholds': {
                '準備': {'min': 2, 'max': 10},
                '定位': {'min': 3, 'max': 15},
                '鎖螺絲': {'min': 5, 'max': 30},
                '檢查': {'min': 2, 'max': 8},
                '完成': {'min': 1, 'max': 5}
            },
            'quality_thresholds': {
                '準備': {'min_confidence': 0.6},
                '定位': {'min_confidence': 0.7},
                '鎖螺絲': {'min_confidence': 0.8},
                '檢查': {'min_confidence': 0.7},
                '完成': {'min_confidence': 0.6}
            }
        },
        'model_params': {
            'sequence_length': 10,
            'confidence_threshold': 0.5,
            'feature_quality_threshold': 0.6
        },
        'output': {
            'save_detailed_logs': True,
            'export_statistics': True,
            'generate_reports': True
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合併默認配置
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        except Exception as e:
            logger.warning(f"載入配置檔案失敗: {e}，使用默認配置")
            return default_config
    else:
        # 創建默認配置檔案
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        return default_config

class SOPMonitor:
    """SOP 監控主類"""
    def __init__(self, config: Dict):
        self.config = config
        self.model = None
        self.scaler = None
        self.action_labels = None
        self.feature_extractor = SOPFeatureExtractor()
        self.anomaly_detector = SOPAnomalyDetector(config['anomaly_detection'])
        self.statistics = SOPStatistics()
        
        # 初始化 MediaPipe
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=1,
            enable_segmentation=False,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # 初始化 YOLO 模型
        self.yolo_model = None
        self.init_yolo_model()
        
        # 儲存歷史數據
        self.coords_history = deque(maxlen=config['model_params']['sequence_length'])
        self.time_history = deque(maxlen=config['model_params']['sequence_length'])
        self.feature_history = deque(maxlen=config['model_params']['sequence_length'])
        
        # 載入模型
        self.load_models()
        
        # 顯示相關
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.6
        self.color_green = (0, 255, 0)
        self.color_red = (0, 0, 255)
        self.color_blue = (255, 0, 0)
        self.color_yellow = (0, 255, 255)
        
    def init_yolo_model(self):
        """初始化 YOLO 模型"""
        try:
            # 嘗試載入 YOLO 模型
            model_path = 'yolov8n.pt'
            if os.path.exists(model_path):
                self.yolo_model = YOLO(model_path)
                logger.info("YOLO 模型載入成功")
            else:
                logger.warning("未找到 YOLO 模型檔案，將下載預設模型")
                self.yolo_model = YOLO('yolov8n.pt')
        except Exception as e:
            logger.error(f"YOLO 模型初始化失敗: {e}")
            self.yolo_model = None
    
    def load_models(self):
        """載入訓練好的模型"""
        try:
            # 載入動作分類模型
            model_path = 'sop_lstm_model.pth'
            if os.path.exists(model_path):
                # 載入模型參數
                checkpoint = torch.load(model_path, map_location=device)
                
                # 創建模型
                self.model = SOPLSTM(
                    input_size=35,
                    hidden_size=64,
                    num_layers=2,
                    num_classes=5,
                    dropout=0.2
                )
                
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.to(device)
                self.model.eval()
                
                # 載入標籤
                self.action_labels = checkpoint.get('action_labels', 
                    ['準備', '定位', '鎖螺絲', '檢查', '完成'])
                
                logger.info("SOP LSTM 模型載入成功")
            else:
                logger.warning("未找到 SOP LSTM 模型檔案")
                
            # 載入特徵標準化器
            scaler_path = 'sop_scaler.pkl'
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                logger.info("特徵標準化器載入成功")
            else:
                logger.warning("未找到特徵標準化器檔案")
                
        except Exception as e:
            logger.error(f"模型載入失敗: {e}")
            self.model = None
            self.scaler = None
    
    def detect_objects(self, frame):
        """使用 YOLO 檢測物件"""
        if self.yolo_model is None:
            return []
        
        try:
            results = self.yolo_model(frame)
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        if confidence > 0.5:  # 信心度閾值
                            detections.append({
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': float(confidence),
                                'class_id': class_id,
                                'class_name': self.yolo_model.names[class_id]
                            })
            
            return detections
            
        except Exception as e:
            logger.error(f"物件檢測失敗: {e}")
            return []
    
    def predict_action(self, features):
        """預測動作"""
        if self.model is None or self.scaler is None:
            return '未知', 0.0, 0.0
        
        try:
            # 標準化特徵
            features_scaled = self.scaler.transform(features.reshape(1, -1))
            
            # 準備輸入數據
            if len(self.feature_history) < self.config['model_params']['sequence_length']:
                # 如果歷史數據不足，用零填充
                padded_features = np.zeros((self.config['model_params']['sequence_length'], 35))
                start_idx = self.config['model_params']['sequence_length'] - len(self.feature_history) - 1
                padded_features[start_idx:start_idx+len(self.feature_history)] = list(self.feature_history)
                padded_features[-1] = features_scaled
                input_features = padded_features
            else:
                input_features = np.array(list(self.feature_history))
            
            # 轉換為張量
            input_tensor = torch.FloatTensor(input_features).unsqueeze(0).to(device)
            
            # 預測
            with torch.no_grad():
                output = self.model(input_tensor)
                probabilities = torch.softmax(output, dim=1)
                predicted_class = torch.argmax(probabilities, dim=1).item()
                confidence = probabilities[0][predicted_class].item()
            
            # 評估特徵品質
            feature_quality = self.feature_extractor.assess_feature_quality(
                list(self.coords_history)
            )
            
            # 獲取動作標籤
            action = self.action_labels[predicted_class] if predicted_class < len(self.action_labels) else '未知'
            
            return action, confidence, feature_quality
            
        except Exception as e:
            logger.error(f"動作預測失敗: {e}")
            return '未知', 0.0, 0.0
    
    def draw_pose_landmarks(self, frame, landmarks):
        """繪製姿態關鍵點"""
        if landmarks:
            self.mp_drawing.draw_landmarks(
                frame, landmarks, self.mp_pose.POSE_CONNECTIONS,
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self.mp_drawing.DrawingSpec(color=(0, 0, 255), thickness=2)
            )
    
    def draw_info_panel(self, frame, action, confidence, feature_quality, anomalies, stats):
        """繪製資訊面板"""
        height, width = frame.shape[:2]
        
        # 創建資訊面板
        panel_width = 400
        panel_height = 300
        panel = np.zeros((panel_height, panel_width, 3), dtype=np.uint8)
        
        # 繪製背景
        cv2.rectangle(panel, (0, 0), (panel_width, panel_height), (50, 50, 50), -1)
        
        # 繪製標題
        cv2.putText(panel, 'SOP Monitor', (10, 30), self.font, 0.8, (255, 255, 255), 2)
        
        # 繪製動作資訊
        y_offset = 60
        cv2.putText(panel, f'Action: {action}', (10, y_offset), self.font, 0.6, (0, 255, 0), 1)
        y_offset += 25
        cv2.putText(panel, f'Confidence: {confidence:.2f}', (10, y_offset), self.font, 0.6, (0, 255, 0), 1)
        y_offset += 25
        cv2.putText(panel, f'Quality: {feature_quality:.2f}', (10, y_offset), self.font, 0.6, (0, 255, 0), 1)
        y_offset += 35
        
        # 繪製異常資訊
        cv2.putText(panel, 'Anomalies:', (10, y_offset), self.font, 0.6, (0, 255, 255), 1)
        y_offset += 25
        
        if anomalies:
            for i, anomaly in enumerate(anomalies[-3:]):  # 顯示最近3個異常
                anomaly_text = f"- {anomaly['type']}: {anomaly['description'][:40]}"
                color = (0, 0, 255) if anomaly['severity'] == 'high' else (0, 255, 255)
                cv2.putText(panel, anomaly_text, (10, y_offset), self.font, 0.4, color, 1)
                y_offset += 20
        else:
            cv2.putText(panel, '- No anomalies detected', (10, y_offset), self.font, 0.5, (0, 255, 0), 1)
            y_offset += 25
        
        # 繪製統計資訊
        y_offset += 10
        cv2.putText(panel, 'Statistics:', (10, y_offset), self.font, 0.6, (255, 255, 0), 1)
        y_offset += 25
        
        total_steps = stats.get('total_steps', 0)
        avg_confidence = stats.get('average_confidence', 0)
        anomaly_rate = stats.get('anomaly_rate', 0)
        
        cv2.putText(panel, f'Total Steps: {total_steps}', (10, y_offset), self.font, 0.5, (255, 255, 255), 1)
        y_offset += 20
        cv2.putText(panel, f'Avg Confidence: {avg_confidence:.2f}', (10, y_offset), self.font, 0.5, (255, 255, 255), 1)
        y_offset += 20
        cv2.putText(panel, f'Anomaly Rate: {anomaly_rate:.2%}', (10, y_offset), self.font, 0.5, (255, 255, 255), 1)
        
        # 將面板貼到主畫面
        frame[10:10+panel_height, width-panel_width-10:width-10] = panel
    
    def draw_objects(self, frame, detections):
        """繪製檢測到的物件"""
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            class_name = detection['class_name']
            
            # 繪製邊界框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 繪製標籤
            label = f"{class_name}: {confidence:.2f}"
            cv2.putText(frame, label, (x1, y1-10), self.font, 0.5, (0, 255, 0), 1)
    
    def process_frame(self, frame):
        """處理單幀影像"""
        current_time = datetime.now().timestamp()
        
        # 轉換為 RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 姿態檢測
        results = self.pose.process(rgb_frame)
        
        # 物件檢測
        detections = self.detect_objects(frame)
        
        # 如果檢測到姿態
        if results.pose_landmarks:
            # 繪製姿態關鍵點
            self.draw_pose_landmarks(frame, results.pose_landmarks)
            
            # 提取座標
            coords = self.feature_extractor.extract_coordinates_from_landmarks(results.pose_landmarks)
            
            # 更新歷史數據
            self.coords_history.append(coords)
            self.time_history.append(current_time)
            
            # 提取特徵
            features = self.feature_extractor.extract_sop_features_from_sequence(
                list(self.coords_history),
                list(self.time_history)
            )
            
            # 更新特徵歷史
            self.feature_history.append(features)
            
            # 預測動作
            action, confidence, feature_quality = self.predict_action(features)
            
            # 檢測異常
            anomalies = self.anomaly_detector.detect_anomalies(action, confidence, current_time)
            
            # 更新統計
            self.statistics.update_stats(action, confidence)
            for anomaly in anomalies:
                self.statistics.update_anomaly_stats(anomaly['type'])
            
            # 獲取統計資訊
            stats = self.statistics.get_performance_metrics()
            
            # 繪製資訊
            self.draw_info_panel(frame, action, confidence, feature_quality, anomalies, stats)
            
            # 繪製物件
            self.draw_objects(frame, detections)
            
            # 在畫面上顯示動作和信心度
            cv2.putText(frame, f'Action: {action}', (10, 30), self.font, 0.8, self.color_green, 2)
            cv2.putText(frame, f'Confidence: {confidence:.2f}', (10, 60), self.font, 0.6, self.color_green, 2)
            cv2.putText(frame, f'Quality: {feature_quality:.2f}', (10, 90), self.font, 0.6, self.color_green, 2)
            
            # 顯示異常警告
            if anomalies:
                cv2.putText(frame, 'ANOMALY DETECTED!', (10, 120), self.font, 0.8, self.color_red, 2)
                for i, anomaly in enumerate(anomalies[-2:]):  # 顯示最近2個異常
                    y_pos = 150 + i * 25
                    cv2.putText(frame, f"{anomaly['type']}: {anomaly['description'][:50]}", 
                              (10, y_pos), self.font, 0.4, self.color_red, 1)
        
        return frame
    
    def run_camera(self, camera_id=0):
        """運行攝像頭檢測"""
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            logger.error("無法開啟攝像頭")
            return
        
        # 設置攝像頭參數
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        logger.info("開始 SOP 監控...")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 處理幀
                processed_frame = self.process_frame(frame)
                
                # 顯示結果
                cv2.imshow('SOP Monitor', processed_frame)
                
                # 按 'q' 退出
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                
                # 按 's' 保存統計報告
                if cv2.waitKey(1) & 0xFF == ord('s'):
                    self.save_statistics_report()
                
                # 按 'r' 重置統計
                if cv2.waitKey(1) & 0xFF == ord('r'):
                    self.statistics.reset_stats()
                    self.anomaly_detector.reset_stats()
                    logger.info("統計數據已重置")
                    
        except KeyboardInterrupt:
            logger.info("用戶中斷程序")
        finally:
            cap.release()
            cv2.destroyAllWindows()
            
            # 保存最終統計報告
            if self.config['output']['generate_reports']:
                self.save_statistics_report()
    
    def save_statistics_report(self):
        """保存統計報告"""
        try:
            # 獲取統計數據
            stats = self.statistics.get_performance_metrics()
            anomaly_records = self.anomaly_detector.anomaly_records
            
            # 創建報告
            report = {
                'timestamp': datetime.now().isoformat(),
                'performance_metrics': stats,
                'anomaly_records': anomaly_records,
                'config': self.config
            }
            
            # 保存為 JSON
            report_filename = f"sop_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"統計報告已保存: {report_filename}")
            
            # 如果需要，生成圖表
            if self.config['output']['export_statistics']:
                self.generate_charts(stats, anomaly_records)
                
        except Exception as e:
            logger.error(f"保存統計報告失敗: {e}")
    
    def generate_charts(self, stats, anomaly_records):
        """生成統計圖表"""
        try:
            # 創建圖表
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('SOP 監控統計報告', fontsize=16)
            
            # 步驟計數圖
            if self.statistics.step_counts:
                steps = list(self.statistics.step_counts.keys())
                counts = list(self.statistics.step_counts.values())
                axes[0, 0].bar(steps, counts)
                axes[0, 0].set_title('步驟執行次數')
                axes[0, 0].set_ylabel('次數')
                
            # 信心度分佈圖
            if self.statistics.confidence_scores:
                all_confidences = []
                for confidences in self.statistics.confidence_scores.values():
                    all_confidences.extend(confidences)
                axes[0, 1].hist(all_confidences, bins=20, alpha=0.7)
                axes[0, 1].set_title('信心度分佈')
                axes[0, 1].set_xlabel('信心度')
                axes[0, 1].set_ylabel('頻率')
                
            # 異常類型統計
            if self.statistics.anomaly_counts:
                anomaly_types = list(self.statistics.anomaly_counts.keys())
                anomaly_counts = list(self.statistics.anomaly_counts.values())
                axes[1, 0].pie(anomaly_counts, labels=anomaly_types, autopct='%1.1f%%')
                axes[1, 0].set_title('異常類型分佈')
                
            # 時間趨勢圖（如果有足夠的數據）
            if len(anomaly_records) > 0:
                timestamps = [record['timestamp'] for record in anomaly_records]
                anomaly_timeline = pd.DataFrame({'timestamp': timestamps})
                anomaly_timeline['timestamp'] = pd.to_datetime(anomaly_timeline['timestamp'], unit='s')
                anomaly_timeline['count'] = 1
                
                # 按小時分組
                hourly_anomalies = anomaly_timeline.groupby(
                    anomaly_timeline['timestamp'].dt.floor('H')
                ).count()
                
                if len(hourly_anomalies) > 0:
                    axes[1, 1].plot(hourly_anomalies.index, hourly_anomalies['count'])
                    axes[1, 1].set_title('異常發生時間趨勢')
                    axes[1, 1].set_xlabel('時間')
                    axes[1, 1].set_ylabel('異常次數')
                    
            plt.tight_layout()
            
            # 保存圖表
            chart_filename = f"sop_charts_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"統計圖表已保存: {chart_filename}")
            
        except Exception as e:
            logger.error(f"生成圖表失敗: {e}")

def main():
    """主函數"""
    print("=" * 50)
    print("SOP 異常行為檢測系統")
    print("=" * 50)
    
    # 載入配置
    config = load_config()
    
    # 創建監控器
    monitor = SOPMonitor(config)
    
    # 顯示幫助資訊
    print("\n控制說明:")
    print("- 按 'q' 退出程序")
    print("- 按 's' 保存統計報告")
    print("- 按 'r' 重置統計數據")
    print("\n開始監控...")
    
    # 運行監控
    monitor.run_camera()

if __name__ == "__main__":
    main()   
