import cv2
import mediapipe as mp
import numpy as np
import torch
import torch.nn as nn
import joblib
import json
import os
import pandas as pd
from ultralytics import YOLO
from collections import deque
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 設置繁體中文字體支援
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 設置設備
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用設備: {device}")

class SOPLSTM(nn.Module):
    """SOP LSTM模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes, dropout=0.2):
        super(SOPLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM層
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )
        
        # 全連接層
        self.fc1 = nn.Linear(hidden_size, 64)
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(64, num_classes)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # LSTM層
        lstm_out, _ = self.lstm(x)
        
        # 取最後一個時間步的輸出
        out = lstm_out[:, -1, :]
        
        # 全連接層
        out = self.fc1(out)
        out = self.relu(out)
        out = self.dropout(out)
        out = self.fc2(out)
        
        return out

def load_pytorch_model():
    """載入 PyTorch 模型和編碼器"""
    try:
        print("載入 PyTorch 模型...")
        
        # 載入編碼器
        label_encoder = joblib.load('pytorch_models/label_encoder_pytorch.pkl')
        scaler = joblib.load('pytorch_models/feature_scaler_pytorch.pkl')
        
        # 載入模型
        model_data = torch.load('pytorch_models/sop_pytorch_model.pth', map_location=device)
        model_config = model_data['model_config']
        
        # 創建模型實例
        model = SOPLSTM(
            input_size=model_config['input_size'],
            hidden_size=model_config['hidden_size'],
            num_layers=model_config['num_layers'],
            num_classes=model_config['num_classes']
        )
        
        # 載入權重
        model.load_state_dict(model_data['model_state_dict'])
        model.to(device)
        model.eval()
        
        print("✅ PyTorch 模型載入成功")
        print(f"模型架構: {model_config}")
        print(f"標籤數量: {len(label_encoder.classes_)}")
        
        return model, label_encoder, scaler
        
    except Exception as e:
        print(f"❌ 模型載入失敗: {e}")
        raise

def load_task_area():
    """載入作業區域設定"""
    config_file = 'task_area_config.json'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def is_person_in_task_area(person_box, task_area, overlap_threshold=0.3):
    """判斷人員是否在作業區域內"""
    if task_area is None:
        return True
    
    px1, py1, px2, py2 = person_box
    tx1, ty1, tx2, ty2 = task_area
    
    # 計算重疊區域
    overlap_x1 = max(px1, tx1)
    overlap_y1 = max(py1, ty1)
    overlap_x2 = min(px2, tx2)
    overlap_y2 = min(py2, ty2)
    
    if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
        person_area = (px2 - px1) * (py2 - py1)
        overlap_ratio = overlap_area / person_area
        return overlap_ratio >= overlap_threshold
    
    return False

class SOPFeatureExtractor:
    """完整的 SOP 特徵提取器"""
    def __init__(self):
        # 定義重要的關鍵點索引（MediaPipe 姿態關鍵點）
        self.keypoints = {
            'nose': 0,
            'left_shoulder': 11, 'right_shoulder': 12,
            'left_elbow': 13, 'right_elbow': 14,
            'left_wrist': 15, 'right_wrist': 16,
            'left_pinky': 17, 'right_pinky': 18,
            'left_index': 19, 'right_index': 20,
            'left_thumb': 21, 'right_thumb': 22,
            'left_hip': 23, 'right_hip': 24
        }

    def extract_coordinates_from_landmarks(self, landmarks):
        """從 MediaPipe landmarks 提取座標"""
        coords = {}

        # 提取關鍵點座標
        points_mapping = {
            '鼻子': 'nose',
            '左肩膀': 'left_shoulder', '右肩膀': 'right_shoulder',
            '左手肘': 'left_elbow', '右手肘': 'right_elbow',
            '左手腕': 'left_wrist', '右手腕': 'right_wrist',
            '左手小指': 'left_pinky', '右手小指': 'right_pinky',
            '左手食指': 'left_index', '右手食指': 'right_index',
            '左手拇指': 'left_thumb', '右手拇指': 'right_thumb',
            '左臀部': 'left_hip', '右臀部': 'right_hip'
        }

        for chinese_name, english_name in points_mapping.items():
            if english_name in self.keypoints:
                idx = self.keypoints[english_name]
                landmark = landmarks.landmark[idx]
                coords[chinese_name] = {
                    'x': landmark.x,
                    'y': landmark.y,
                    'z': landmark.z,
                    'visibility': landmark.visibility
                }

        return coords

    def calculate_distance(self, p1, p2):
        """計算兩點間的3D距離"""
        return np.sqrt((p1['x'] - p2['x'])**2 +
                      (p1['y'] - p2['y'])**2 +
                      (p1['z'] - p2['z'])**2)

    def calculate_angle(self, p1, p2, p3):
        """計算三點形成的角度"""
        # 向量 p2->p1 和 p2->p3
        v1 = np.array([p1['x'] - p2['x'], p1['y'] - p2['y'], p1['z'] - p2['z']])
        v2 = np.array([p3['x'] - p2['x'], p3['y'] - p2['y'], p3['z'] - p2['z']])

        # 計算角度
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi

        return angle

    def calculate_wrist_angle(self, wrist, elbow, shoulder):
        """計算手腕旋轉角度（用於檢測螺絲旋轉動作）"""
        # 計算手腕在XY平面的旋轉角度
        wrist_vector = np.array([wrist['x'] - elbow['x'], wrist['y'] - elbow['y']])
        reference_vector = np.array([shoulder['x'] - elbow['x'], shoulder['y'] - elbow['y']])

        # 計算兩向量的角度
        dot_product = np.dot(wrist_vector, reference_vector)
        norms = np.linalg.norm(wrist_vector) * np.linalg.norm(reference_vector)
        cos_angle = dot_product / (norms + 1e-8)  # 避免除零

        angle = np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi
        return angle

    def calculate_velocity(self, coords_sequence, time_sequence):
        """計算速度"""
        if len(coords_sequence) < 2:
            return {point: {'magnitude': 0} for point in coords_sequence[0].keys()}

        velocity = {}
        dt = time_sequence[-1] - time_sequence[0] + 1e-8  # 避免除零

        for point in coords_sequence[0].keys():
            # 計算位置變化
            dx = coords_sequence[-1][point]['x'] - coords_sequence[0][point]['x']
            dy = coords_sequence[-1][point]['y'] - coords_sequence[0][point]['y']
            dz = coords_sequence[-1][point]['z'] - coords_sequence[0][point]['z']

            # 計算速度大小
            magnitude = np.sqrt(dx**2 + dy**2 + dz**2) / dt
            velocity[point] = {'magnitude': magnitude}

        return velocity

    def extract_sop_features_from_sequence(self, coords_sequence, time_sequence):
        """從座標序列提取 SOP 特徵"""
        if len(coords_sequence) == 0:
            return np.zeros(32)

        # 使用最新的座標
        coords = coords_sequence[-1]

        # 計算速度
        velocity = self.calculate_velocity(coords_sequence, time_sequence)

        features = []

        # 1. 手部關鍵特徵（鎖螺絲動作）
        # 肘關節角度
        left_elbow_angle = self.calculate_angle(
            coords['左肩膀'], coords['左手肘'], coords['左手腕']
        )
        right_elbow_angle = self.calculate_angle(
            coords['右肩膀'], coords['右手肘'], coords['右手腕']
        )
        features.extend([left_elbow_angle, right_elbow_angle])

        # 手腕角度（螺絲旋轉檢測）
        left_wrist_angle = self.calculate_wrist_angle(
            coords['左手腕'], coords['左手肘'], coords['左肩膀']
        )
        right_wrist_angle = self.calculate_wrist_angle(
            coords['右手腕'], coords['右手肘'], coords['右肩膀']
        )
        features.extend([left_wrist_angle, right_wrist_angle])

        # 手腕到肩膀距離（手臂伸展程度）
        left_arm_extension = self.calculate_distance(
            coords['左手腕'], coords['左肩膀']
        )
        right_arm_extension = self.calculate_distance(
            coords['右手腕'], coords['右肩膀']
        )
        features.extend([left_arm_extension, right_arm_extension])

        # 2. 手部動作速度
        left_wrist_speed = velocity['左手腕']['magnitude']
        right_wrist_speed = velocity['右手腕']['magnitude']
        features.extend([left_wrist_speed, right_wrist_speed])

        # 3. 雙手協調性
        hands_distance = self.calculate_distance(
            coords['左手腕'], coords['右手腕']
        )
        features.append(hands_distance)

        # 4. 工作姿勢特徵
        # 肩膀水平度
        shoulder_level = abs(coords['左肩膀']['y'] - coords['右肩膀']['y'])
        features.append(shoulder_level)

        # 身體前傾程度
        body_center_x = (coords['左肩膀']['x'] + coords['右肩膀']['x']) / 2
        hip_center_x = (coords['左臀部']['x'] + coords['右臀部']['x']) / 2
        body_lean = abs(body_center_x - hip_center_x)
        features.append(body_lean)

        # 頭部位置
        shoulder_center_y = (coords['左肩膀']['y'] + coords['右肩膀']['y']) / 2
        head_position = coords['鼻子']['y'] - shoulder_center_y
        features.append(head_position)

        # 5. 手指精細動作
        # 手指到手腕距離
        left_finger_spread = (
            self.calculate_distance(coords['左手拇指'], coords['左手食指']) +
            self.calculate_distance(coords['左手食指'], coords['左手小指'])
        ) / 2

        right_finger_spread = (
            self.calculate_distance(coords['右手拇指'], coords['右手食指']) +
            self.calculate_distance(coords['右手食指'], coords['右手小指'])
        ) / 2
        features.extend([left_finger_spread, right_finger_spread])

        # 補充特徵到32個
        while len(features) < 32:
            features.append(0.0)

        return np.array(features[:32])

def process_video(video_path, model, label_encoder, scaler):
    """處理影片並進行 SOP 步驟預測"""
    print(f"開始處理影片: {video_path}")

    # 初始化
    mp_pose = mp.solutions.pose
    pose = mp_pose.Pose(
        static_image_mode=False,
        model_complexity=2,
        enable_segmentation=False,
        min_detection_confidence=0.3,
        min_tracking_confidence=0.3
    )
    yolo_model = YOLO('yolov8n.pt')

    # 初始化特徵提取器
    feature_extractor = SOPFeatureExtractor()

    # 載入作業區域
    task_area = load_task_area()

    # 開啟影片
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ 無法開啟影片: {video_path}")
        return

    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"影片資訊: {total_frames} 幀, {fps:.2f} FPS")

    # 用於存儲結果
    predictions = []
    confidences = []
    frame_numbers = []
    coords_sequence = deque(maxlen=10)  # 保持10幀的座標序列
    time_sequence = deque(maxlen=10)    # 保持10幀的時間序列

    frame_count = 0

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1
        current_time = frame_count / fps

        # YOLO 人員檢測
        results = yolo_model(frame, verbose=False)

        person_detected = False
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    if int(box.cls[0]) == 0:  # 人員類別
                        person_box = box.xyxy[0].cpu().numpy()

                        if is_person_in_task_area(person_box, task_area):
                            person_detected = True

                            # 裁剪人員區域
                            x1, y1, x2, y2 = map(int, person_box)
                            person_roi = frame[y1:y2, x1:x2]

                            # MediaPipe 姿態檢測
                            person_roi_rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
                            pose_results = pose.process(person_roi_rgb)

                            if pose_results.pose_landmarks:
                                # 提取座標
                                coords = feature_extractor.extract_coordinates_from_landmarks(pose_results.pose_landmarks)
                                coords_sequence.append(coords)
                                time_sequence.append(current_time)

                            break

        # 如果有足夠的姿態序列，進行預測
        if len(coords_sequence) == 10:
            try:
                # 計算 SOP 特徵
                sop_features = feature_extractor.extract_sop_features_from_sequence(
                    list(coords_sequence), list(time_sequence)
                )

                # 標準化特徵
                sop_features_scaled = scaler.transform(sop_features.reshape(1, -1))

                # 創建序列（這裡簡化為重複特徵）
                sequence = np.tile(sop_features_scaled, (10, 1)).reshape(1, 10, -1)

                # 轉換為 PyTorch tensor
                sequence_tensor = torch.FloatTensor(sequence).to(device)

                # 預測
                with torch.no_grad():
                    outputs = model(sequence_tensor)
                    probabilities = torch.softmax(outputs, dim=1)
                    predicted_class = torch.argmax(outputs, dim=1).cpu().numpy()[0]
                    confidence = torch.max(probabilities).cpu().numpy()

                # 解碼預測結果
                predicted_label = label_encoder.inverse_transform([predicted_class])[0]

                predictions.append(predicted_label)
                confidences.append(confidence)
                frame_numbers.append(frame_count)

                # 顯示進度
                if frame_count % 30 == 0:
                    print(f"處理進度: {frame_count}/{total_frames} 幀 ({current_time:.1f}s) - 當前預測: {predicted_label} ({confidence:.2f})")

            except Exception as e:
                print(f"預測錯誤 (幀 {frame_count}): {e}")
                import traceback
                traceback.print_exc()

    cap.release()
    pose.close()

    return predictions, confidences, frame_numbers, fps

def save_results(predictions, confidences, frame_numbers, fps, video_path):
    """保存預測結果"""
    print("保存預測結果...")
    
    # 創建結果 DataFrame
    results_df = pd.DataFrame({
        '幀數': frame_numbers,
        '時間_秒': [f/fps for f in frame_numbers],
        '預測標籤': predictions,
        '信心度': confidences
    })
    
    # 保存 CSV
    output_file = f"{os.path.splitext(video_path)[0]}_預測結果.csv"
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"✅ 結果已保存: {output_file}")
    
    # 統計分析
    print("\n📊 預測結果統計:")
    label_counts = results_df['預測標籤'].value_counts()
    for label, count in label_counts.items():
        percentage = (count / len(results_df)) * 100
        print(f"  {label}: {count} 次 ({percentage:.1f}%)")
    
    print(f"\n平均信心度: {np.mean(confidences):.3f}")
    print(f"總預測幀數: {len(predictions)}")
    
    return results_df

def main():
    """主函數"""
    print("=" * 60)
    print("PyTorch SOP 模型推論測試")
    print("=" * 60)
    
    try:
        # 載入模型
        model, label_encoder, scaler = load_pytorch_model()
        
        # 處理影片
        video_path = "2.mp4"
        if not os.path.exists(video_path):
            print(f"❌ 找不到影片檔案: {video_path}")
            return
        
        predictions, confidences, frame_numbers, fps = process_video(
            video_path, model, label_encoder, scaler
        )
        
        if predictions:
            # 保存結果
            results_df = save_results(predictions, confidences, frame_numbers, fps, video_path)
            
            print("\n" + "=" * 60)
            print("✅ 推論完成！")
            print(f"📁 結果檔案: {os.path.splitext(video_path)[0]}_預測結果.csv")
            print("=" * 60)
        else:
            print("❌ 沒有檢測到有效的姿態序列")
            
    except Exception as e:
        print(f"❌ 推論過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
