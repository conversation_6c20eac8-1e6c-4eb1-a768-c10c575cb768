import cv2
import mediapipe as mp
import numpy as np
import torch
import torch.nn as nn
import joblib
import json
import os
import pandas as pd
from ultralytics import YOLO
from collections import deque
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 設置繁體中文字體支援
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 設置設備
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用設備: {device}")

class SOPLSTM(nn.Module):
    """SOP LSTM模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes, dropout=0.2):
        super(SOPLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM層
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )
        
        # 全連接層
        self.fc1 = nn.Linear(hidden_size, 64)
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(64, num_classes)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # LSTM層
        lstm_out, _ = self.lstm(x)
        
        # 取最後一個時間步的輸出
        out = lstm_out[:, -1, :]
        
        # 全連接層
        out = self.fc1(out)
        out = self.relu(out)
        out = self.dropout(out)
        out = self.fc2(out)
        
        return out

def load_pytorch_model():
    """載入 PyTorch 模型和編碼器"""
    try:
        print("載入 PyTorch 模型...")
        
        # 載入編碼器
        label_encoder = joblib.load('pytorch_models/label_encoder_pytorch.pkl')
        scaler = joblib.load('pytorch_models/feature_scaler_pytorch.pkl')
        
        # 載入模型
        model_data = torch.load('pytorch_models/sop_pytorch_model.pth', map_location=device)
        model_config = model_data['model_config']
        
        # 創建模型實例
        model = SOPLSTM(
            input_size=model_config['input_size'],
            hidden_size=model_config['hidden_size'],
            num_layers=model_config['num_layers'],
            num_classes=model_config['num_classes']
        )
        
        # 載入權重
        model.load_state_dict(model_data['model_state_dict'])
        model.to(device)
        model.eval()
        
        print("✅ PyTorch 模型載入成功")
        print(f"模型架構: {model_config}")
        print(f"標籤數量: {len(label_encoder.classes_)}")
        
        return model, label_encoder, scaler
        
    except Exception as e:
        print(f"❌ 模型載入失敗: {e}")
        raise

def load_task_area():
    """載入作業區域設定"""
    config_file = 'task_area_config.json'
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get('task_area', None)
        except:
            return None
    return None

def center(box):
    """計算bounding box中心點"""
    x1, y1, x2, y2 = box
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def distance(p1, p2):
    """計算兩點距離"""
    return ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5

def is_person_in_task_area(person_box, task_area, overlap_threshold=0.3):
    """判斷人員是否在作業區域內"""
    if task_area is None:
        return True
    
    px1, py1, px2, py2 = person_box
    tx1, ty1, tx2, ty2 = task_area
    
    # 計算重疊區域
    overlap_x1 = max(px1, tx1)
    overlap_y1 = max(py1, ty1)
    overlap_x2 = min(px2, tx2)
    overlap_y2 = min(py2, ty2)
    
    if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
        person_area = (px2 - px1) * (py2 - py1)
        overlap_ratio = overlap_area / person_area
        return overlap_ratio >= overlap_threshold
    
    return False

def extract_pose_features(landmarks):
    """從姿態關鍵點提取特徵"""
    if landmarks is None:
        return None
    
    features = []
    
    # 提取所有關鍵點的座標和可見度
    for landmark in landmarks.landmark:
        features.extend([landmark.x, landmark.y, landmark.z, landmark.visibility])
    
    return np.array(features)

def calculate_sop_features(pose_sequence):
    """計算 SOP 特徵（簡化版本）"""
    if len(pose_sequence) == 0:
        return np.zeros(32)  # 返回32個特徵的零向量
    
    # 這裡應該實現完整的特徵計算邏輯
    # 為了簡化，我們使用基本的統計特徵
    pose_array = np.array(pose_sequence)
    
    features = []
    # 計算均值、標準差等統計特徵
    features.extend(np.mean(pose_array, axis=0)[:8])  # 前8個特徵的均值
    features.extend(np.std(pose_array, axis=0)[:8])   # 前8個特徵的標準差
    features.extend(np.max(pose_array, axis=0)[:8])   # 前8個特徵的最大值
    features.extend(np.min(pose_array, axis=0)[:8])   # 前8個特徵的最小值
    
    return np.array(features[:32])

def process_video(video_path, model, label_encoder, scaler):
    """處理影片並進行 SOP 步驟預測"""
    print(f"開始處理影片: {video_path}")
    
    # 初始化
    mp_pose = mp.solutions.pose
    pose = mp_pose.Pose(
        static_image_mode=False,
        model_complexity=2,
        enable_segmentation=False,
        min_detection_confidence=0.3,
        min_tracking_confidence=0.3
    )
    yolo_model = YOLO('yolov8n.pt')
    
    # 載入作業區域
    task_area = load_task_area()
    
    # 開啟影片
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ 無法開啟影片: {video_path}")
        return
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"影片資訊: {total_frames} 幀, {fps:.2f} FPS")
    
    # 用於存儲結果
    predictions = []
    confidences = []
    frame_numbers = []
    pose_sequence = deque(maxlen=10)  # 保持10幀的序列
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # YOLO 人員檢測
        results = yolo_model(frame, verbose=False)
        
        person_detected = False
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    if int(box.cls[0]) == 0:  # 人員類別
                        person_box = box.xyxy[0].cpu().numpy()
                        
                        if is_person_in_task_area(person_box, task_area):
                            person_detected = True
                            
                            # 裁剪人員區域
                            x1, y1, x2, y2 = map(int, person_box)
                            person_roi = frame[y1:y2, x1:x2]
                            
                            # MediaPipe 姿態檢測
                            person_roi_rgb = cv2.cvtColor(person_roi, cv2.COLOR_BGR2RGB)
                            pose_results = pose.process(person_roi_rgb)
                            
                            if pose_results.pose_landmarks:
                                # 提取姿態特徵
                                pose_features = extract_pose_features(pose_results.pose_landmarks)
                                if pose_features is not None:
                                    pose_sequence.append(pose_features)
                            
                            break
        
        # 如果有足夠的姿態序列，進行預測
        if len(pose_sequence) == 10:
            try:
                # 計算 SOP 特徵
                sop_features = calculate_sop_features(list(pose_sequence))
                
                # 標準化特徵
                sop_features_scaled = scaler.transform(sop_features.reshape(1, -1))
                
                # 創建序列（這裡簡化為重複特徵）
                sequence = np.tile(sop_features_scaled, (10, 1)).reshape(1, 10, -1)
                
                # 轉換為 PyTorch tensor
                sequence_tensor = torch.FloatTensor(sequence).to(device)
                
                # 預測
                with torch.no_grad():
                    outputs = model(sequence_tensor)
                    probabilities = torch.softmax(outputs, dim=1)
                    predicted_class = torch.argmax(outputs, dim=1).cpu().numpy()[0]
                    confidence = torch.max(probabilities).cpu().numpy()
                
                # 解碼預測結果
                predicted_label = label_encoder.inverse_transform([predicted_class])[0]
                
                predictions.append(predicted_label)
                confidences.append(confidence)
                frame_numbers.append(frame_count)
                
                # 顯示進度
                if frame_count % 30 == 0:
                    time_sec = frame_count / fps
                    print(f"處理進度: {frame_count}/{total_frames} 幀 ({time_sec:.1f}s) - 當前預測: {predicted_label} ({confidence:.2f})")
                
            except Exception as e:
                print(f"預測錯誤 (幀 {frame_count}): {e}")
    
    cap.release()
    pose.close()
    
    return predictions, confidences, frame_numbers, fps

def save_results(predictions, confidences, frame_numbers, fps, video_path):
    """保存預測結果"""
    print("保存預測結果...")
    
    # 創建結果 DataFrame
    results_df = pd.DataFrame({
        '幀數': frame_numbers,
        '時間_秒': [f/fps for f in frame_numbers],
        '預測標籤': predictions,
        '信心度': confidences
    })
    
    # 保存 CSV
    output_file = f"{os.path.splitext(video_path)[0]}_預測結果.csv"
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"✅ 結果已保存: {output_file}")
    
    # 統計分析
    print("\n📊 預測結果統計:")
    label_counts = results_df['預測標籤'].value_counts()
    for label, count in label_counts.items():
        percentage = (count / len(results_df)) * 100
        print(f"  {label}: {count} 次 ({percentage:.1f}%)")
    
    print(f"\n平均信心度: {np.mean(confidences):.3f}")
    print(f"總預測幀數: {len(predictions)}")
    
    return results_df

def main():
    """主函數"""
    print("=" * 60)
    print("PyTorch SOP 模型推論測試")
    print("=" * 60)
    
    try:
        # 載入模型
        model, label_encoder, scaler = load_pytorch_model()
        
        # 處理影片
        video_path = "2.mp4"
        if not os.path.exists(video_path):
            print(f"❌ 找不到影片檔案: {video_path}")
            return
        
        predictions, confidences, frame_numbers, fps = process_video(
            video_path, model, label_encoder, scaler
        )
        
        if predictions:
            # 保存結果
            results_df = save_results(predictions, confidences, frame_numbers, fps, video_path)
            
            print("\n" + "=" * 60)
            print("✅ 推論完成！")
            print(f"📁 結果檔案: {os.path.splitext(video_path)[0]}_預測結果.csv")
            print("=" * 60)
        else:
            print("❌ 沒有檢測到有效的姿態序列")
            
    except Exception as e:
        print(f"❌ 推論過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
