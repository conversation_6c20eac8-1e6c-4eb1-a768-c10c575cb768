import numpy as np
import pandas as pd
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, Masking
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter

class SOPLSTMTrainer:
    def __init__(self, csv_file='sop_features_已標記_繁體中文.csv'):
        self.csv_file = csv_file
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.model = None
        self.sequence_length = 10  # 10幀作為一個序列（約0.33秒），適應較短的動作
        
    def load_and_preprocess_data(self):
        """載入和預處理資料"""
        print("正在載入資料...")
        df = pd.read_csv(self.csv_file, encoding='utf-8-sig')
        
        print(f"原始資料形狀: {df.shape}")
        print(f"標籤分佈:")
        label_counts = df['標籤'].value_counts()
        for label, count in label_counts.items():
            print(f"  {label}: {count} 行")
        
        # 保留所有標籤，不進行過濾
        df_filtered = df.copy()
        print(f"保留所有標籤，資料形狀: {df_filtered.shape}")
        print("注意：保留所有詳細標籤，包括資料較少的標籤")
        
        # 選擇特徵欄位（排除非數值欄位）
        feature_columns = []
        exclude_columns = ['幀數', '時間_秒', '標籤', '時間_分秒']
        
        for col in df_filtered.columns:
            if col not in exclude_columns:
                # 檢查是否為數值欄位
                if df_filtered[col].dtype in ['int64', 'float64', 'bool']:
                    feature_columns.append(col)
                elif col.endswith('正常'):  # 布林欄位
                    # 將布林值轉換為數值
                    df_filtered[col] = df_filtered[col].astype(int)
                    feature_columns.append(col)
        
        print(f"選擇的特徵欄位數量: {len(feature_columns)}")
        print(f"特徵欄位: {feature_columns[:10]}...")  # 顯示前10個
        
        # 處理缺失值
        df_filtered[feature_columns] = df_filtered[feature_columns].fillna(0)
        
        return df_filtered, feature_columns
    
    def create_sequences(self, df, feature_columns):
        """創建時間序列資料"""
        print("正在創建時間序列...")

        X, y = [], []

        # 按標籤分組，為每個連續的標籤段創建序列
        current_label = None
        current_sequence = []

        for idx, row in df.iterrows():
            label = row['標籤']
            features = row[feature_columns].values

            if label != current_label:
                # 標籤改變，保存之前的序列
                if len(current_sequence) >= self.sequence_length:
                    # 創建滑動窗口序列
                    for i in range(len(current_sequence) - self.sequence_length + 1):
                        X.append(current_sequence[i:i + self.sequence_length])
                        y.append(current_label)
                elif len(current_sequence) > 0:
                    # 對於較短的序列，進行填充或重複
                    if len(current_sequence) >= 3:  # 至少3幀才創建序列
                        # 重複最後幾幀來達到所需長度
                        padded_sequence = current_sequence.copy()
                        while len(padded_sequence) < self.sequence_length:
                            padded_sequence.append(current_sequence[-1])
                        X.append(padded_sequence[:self.sequence_length])
                        y.append(current_label)

                # 開始新的序列
                current_label = label
                current_sequence = [features]
            else:
                current_sequence.append(features)

        # 處理最後一個序列
        if len(current_sequence) >= self.sequence_length:
            for i in range(len(current_sequence) - self.sequence_length + 1):
                X.append(current_sequence[i:i + self.sequence_length])
                y.append(current_label)
        elif len(current_sequence) >= 3:
            # 對最後一個序列也進行填充
            padded_sequence = current_sequence.copy()
            while len(padded_sequence) < self.sequence_length:
                padded_sequence.append(current_sequence[-1])
            X.append(padded_sequence[:self.sequence_length])
            y.append(current_label)

        X = np.array(X)
        y = np.array(y)

        print(f"創建的序列數量: {len(X)}")
        print(f"序列形狀: {X.shape}")
        print(f"標籤分佈:")
        label_counts = Counter(y)
        for label, count in label_counts.items():
            print(f"  {label}: {count} 序列")

        return X, y
    
    def prepare_data(self):
        """準備訓練資料"""
        # 載入資料
        df, feature_columns = self.load_and_preprocess_data()
        
        # 創建序列
        X, y = self.create_sequences(df, feature_columns)
        
        if len(X) == 0:
            raise ValueError("沒有足夠的資料創建序列，請檢查資料或降低sequence_length")
        
        # 標準化特徵
        print("正在標準化特徵...")
        X_reshaped = X.reshape(-1, X.shape[-1])
        X_scaled = self.scaler.fit_transform(X_reshaped)
        X = X_scaled.reshape(X.shape)
        
        # 編碼標籤
        print("正在編碼標籤...")
        y_encoded = self.label_encoder.fit_transform(y)
        y_categorical = to_categorical(y_encoded)
        
        # 分割訓練和測試集
        # 檢查是否有標籤只有1個樣本，如果有則不使用stratify
        unique_labels, label_counts = np.unique(y_encoded, return_counts=True)
        min_count = np.min(label_counts)

        if min_count == 1:
            print("警告：某些標籤只有1個樣本，將不使用分層抽樣")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_categorical, test_size=0.2, random_state=42
            )
        else:
            X_train, X_test, y_train, y_test = train_test_split(
                X, y_categorical, test_size=0.2, random_state=42, stratify=y_encoded
            )
        
        print(f"訓練集形狀: {X_train.shape}")
        print(f"測試集形狀: {X_test.shape}")
        
        return X_train, X_test, y_train, y_test, feature_columns
    
    def build_model(self, input_shape, num_classes):
        """建立LSTM模型"""
        print("正在建立LSTM模型...")
        
        model = Sequential([
            Masking(mask_value=0.0, input_shape=input_shape),
            LSTM(128, return_sequences=True, dropout=0.2),
            LSTM(64, dropout=0.2),
            Dense(32, activation='relu'),
            Dropout(0.3),
            Dense(num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("模型架構:")
        model.summary()
        
        return model
    
    def train_model(self, X_train, X_test, y_train, y_test):
        """訓練模型"""
        print("正在訓練模型...")
        
        # 建立模型
        input_shape = (X_train.shape[1], X_train.shape[2])
        num_classes = y_train.shape[1]
        self.model = self.build_model(input_shape, num_classes)
        
        # 設定回調函數
        callbacks = [
            EarlyStopping(patience=10, restore_best_weights=True),
            ModelCheckpoint('best_sop_model.h5', save_best_only=True)
        ]
        
        # 訓練模型
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=100,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )
        
        return history
    
    def evaluate_model(self, X_test, y_test):
        """評估模型"""
        print("正在評估模型...")
        
        # 預測
        y_pred = self.model.predict(X_test)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(y_test, axis=1)
        
        # 計算準確率
        accuracy = np.mean(y_pred_classes == y_true_classes)
        print(f"測試集準確率: {accuracy:.4f}")
        
        # 顯示分類報告
        from sklearn.metrics import classification_report, confusion_matrix
        
        target_names = self.label_encoder.classes_
        print("\n分類報告:")
        print(classification_report(y_true_classes, y_pred_classes, target_names=target_names))
        
        # 混淆矩陣
        cm = confusion_matrix(y_true_classes, y_pred_classes)
        plt.figure(figsize=(12, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=target_names, yticklabels=target_names)
        plt.title('混淆矩陣')
        plt.ylabel('真實標籤')
        plt.xlabel('預測標籤')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return accuracy
    
    def save_model_and_encoders(self):
        """保存模型和編碼器"""
        print("正在保存模型和編碼器...")
        
        # 保存模型
        self.model.save('sop_lstm_model.h5')
        
        # 保存編碼器
        joblib.dump(self.label_encoder, 'label_encoder.pkl')
        joblib.dump(self.scaler, 'feature_scaler.pkl')
        
        print("模型已保存為: sop_lstm_model.h5")
        print("標籤編碼器已保存為: label_encoder.pkl")
        print("特徵縮放器已保存為: feature_scaler.pkl")
    
    def train(self):
        """完整的訓練流程"""
        try:
            # 準備資料
            X_train, X_test, y_train, y_test, feature_columns = self.prepare_data()
            
            # 訓練模型
            history = self.train_model(X_train, X_test, y_train, y_test)
            
            # 評估模型
            accuracy = self.evaluate_model(X_test, y_test)
            
            # 保存模型
            self.save_model_and_encoders()
            
            # 繪製訓練歷史
            self.plot_training_history(history)
            
            print(f"\n訓練完成！最終測試準確率: {accuracy:.4f}")
            
        except Exception as e:
            print(f"訓練過程中發生錯誤: {e}")
            import traceback
            traceback.print_exc()
    
    def plot_training_history(self, history):
        """繪製訓練歷史"""
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        plt.plot(history.history['loss'], label='訓練損失')
        plt.plot(history.history['val_loss'], label='驗證損失')
        plt.title('模型損失')
        plt.xlabel('Epoch')
        plt.ylabel('損失')
        plt.legend()
        
        plt.subplot(1, 2, 2)
        plt.plot(history.history['accuracy'], label='訓練準確率')
        plt.plot(history.history['val_accuracy'], label='驗證準確率')
        plt.title('模型準確率')
        plt.xlabel('Epoch')
        plt.ylabel('準確率')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    print("SOP LSTM 模型訓練器")
    print("=" * 50)
    
    # 創建訓練器
    trainer = SOPLSTMTrainer()
    
    # 開始訓練
    trainer.train()

if __name__ == "__main__":
    main()
