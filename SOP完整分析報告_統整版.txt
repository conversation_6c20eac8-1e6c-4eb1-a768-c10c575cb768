
SOP姿態監控完整分析報告
====================================================================================================
報告生成時間：2025年07月10日 17:20:01
資料來源：sop_features_繁體中文.csv
====================================================================================================

本報告結合特徵定義說明與實際數據分析，提供完整的SOP監控解讀指南。

====================================================================================================
第一部分：資料概覽
====================================================================================================

基本資訊：
• 總幀數：1,315 幀
• 總時長：52.99 秒 (00:52)
• 平均幀率：24.82 FPS
• 特徵數量：35 個
• 資料完整性：97.1%

====================================================================================================
第二部分：特徵詳細定義與分析
====================================================================================================


🔧 2.1 角度特徵分析
────────────────────────────────────────────────────────────────────────────────

【左手肘角度 / 右手肘角度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：肩膀-手肘-手腕三點形成的角度                                        │
│ • 計算方式：使用向量夾角公式計算                                            │
│ • 數值範圍：0° - 180°                                                      │
│ • 正常範圍：90° - 150°                                                     │
│ • 判斷標準：< 90°(過度彎曲) | 90°-150°(正常) | >150°(過度伸展)             │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
│ • 左手肘角度：平均 129.1°，範圍 84.1° - 167.3°                    │
│   └─ 正常範圍內：89.2% (1,173/1,315 幀)                        │
│ • 右手肘角度：平均 127.7°，範圍 67.5° - 173.3°                    │
│   └─ 正常範圍內：74.8% (984/1,315 幀)                        │
└────────────────────────────────────────────────────────────────────────────┘

【左手腕角度 / 右手腕角度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：手腕相對於手肘-肩膀參考線的旋轉角度                                 │
│ • 計算方式：計算手腕向量與參考向量的夾角                                    │
│ • 數值範圍：0° - 180°                                                      │
│ • 旋轉閾值：> 15°                                                          │
│ • 判斷標準：>15°(檢測到旋轉) | ≤15°(無明顯旋轉)                            │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
│ • 左手腕角度：平均 135.7°                                              │
│   └─ 檢測到旋轉：100.0% (1,315/1,315 幀)                      │
│ • 右手腕角度：平均 133.5°                                              │
│   └─ 檢測到旋轉：100.0% (1,315/1,315 幀)                      │
└────────────────────────────────────────────────────────────────────────────┘


⚡ 2.2 速度特徵分析
────────────────────────────────────────────────────────────────────────────────

【左手腕速度 / 右手腕速度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：手腕移動的瞬時速度大小                                              │
│ • 計算方式：√[vₓ² + vᵧ² + vᵤ²]，其中v = Δ座標/Δ時間                        │
│ • 數值範圍：0.0 - 無上限                                                   │
│ • 正常範圍：0.01 - 0.5                                                     │
│ • 單位含義：標準化座標/秒 (1.0 ≈ 每秒橫跨整個畫面)                         │
│ • 判斷標準：<0.01(靜止) | 0.01-0.5(正常) | >0.5(過快)                      │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
│ • 左手腕速度：平均 1.434，最高 12.872                              │
│   ├─ 正常速度：23.7% (312/1,315 幀)                          │
│   └─ 過快動作：76.3% (1,003/1,315 幀)                            │
│ • 右手腕速度：平均 1.017，最高 14.139                              │
│   ├─ 正常速度：35.4% (465/1,315 幀)                          │
│   └─ 過快動作：64.6% (850/1,315 幀)                            │
└────────────────────────────────────────────────────────────────────────────┘


🧍 2.3 姿勢特徵分析
────────────────────────────────────────────────────────────────────────────────

【肩膀水平度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：左右肩膀Y座標差的絕對值                                             │
│ • 計算方式：|左肩Y座標 - 右肩Y座標|                                         │
│ • 數值範圍：0.0 - 1.0                                                      │
│ • 正常標準：< 0.1                                                          │
│ • 判斷標準：<0.1(肩膀水平) | ≥0.1(肩膀傾斜)                                │
└────────────────────────────────────────────────────────────────────────────┘

【身體前傾程度】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：肩膀中心與臀部中心X座標差的絕對值                                   │
│ • 計算方式：|肩膀中心X - 臀部中心X|                                         │
│ • 數值範圍：0.0 - 1.0                                                      │
│ • 正常標準：< 0.2                                                          │
│ • 判斷標準：<0.2(正常姿勢) | ≥0.2(過度前傾)                                │
└────────────────────────────────────────────────────────────────────────────┘

【頭部位置】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：鼻子相對於肩膀中心的Y座標差                                         │
│ • 計算方式：鼻子Y座標 - 肩膀中心Y座標                                       │
│ • 數值範圍：-1.0 - 1.0                                                     │
│ • 正常標準：> -0.3                                                         │
│ • 判斷標準：>-0.3(頭部位置正常) | ≤-0.3(過度低頭)                          │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
│ • 肩膀水平度：平均 0.208                                              │
│   └─ 正常水平：2.6% (34/1,315 幀)                            │
│ • 身體前傾程度：平均 0.150                                            │
│   └─ 正常姿勢：71.8% (944/1,315 幀)                              │
│ • 頭部位置：平均 0.067                                                │
│   └─ 位置正常：100.0% (1,315/1,315 幀)                              │
└────────────────────────────────────────────────────────────────────────────┘


📊 2.4 SOP合規性綜合分析
────────────────────────────────────────────────────────────────────────────────

【SOP合規性評分】
┌─ 特徵定義 ─────────────────────────────────────────────────────────────────┐
│ • 定義：所有檢查項目的通過比例                                              │
│ • 計算方式：通過項目數 ÷ 總檢查項目數                                       │
│ • 數值範圍：0.0 - 1.0                                                      │
│ • 評分等級：0.8+(優秀) | 0.6-0.8(良好) | 0.4-0.6(普通) | 0.0-0.4(需改進)   │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 實際數據分析 ─────────────────────────────────────────────────────────────┐
│ • 平均評分：0.632 (63.2%)                                        │
│ • 評分範圍：0.375 - 0.875 (37.5% - 87.5%)                              │
│ • 評分分佈：                                                                │
│   ├─ 優秀 (≥80%)：8 幀 (0.6%)                                │
│   ├─ 良好 (60-79%)：1,037 幀 (78.9%)                              │
│   ├─ 普通 (40-59%)：266 幀 (20.2%)                              │
│   └─ 需改進 (<40%)：4 幀 (0.3%)                              │
└────────────────────────────────────────────────────────────────────────────┘


┌─ 各項檢查詳細分析 ─────────────────────────────────────────────────────────┐
│ 檢查項目           │ 通過率   │ 通過幀數     │ 檢查標準                    │
├───────────────────┼─────────┼─────────────┼────────────────────────────┤
│ 手肘角度正常          │   68.2% │    897/1,315 │ 左右手肘角度都在90°-150°範圍內        │
│ 手腕旋轉正常          │  100.0% │  1,315/1,315 │ 左手腕角度>15° 或 右手腕角度>15°      │
│ 動作速度正常          │   35.4% │    465/1,315 │ 右手腕速度在0.01-0.5範圍內          │
│ 軌跡規律正常          │   99.2% │  1,305/1,315 │ 左手或右手軌跡規律性>0.3             │
│ 姿勢正常            │    2.6% │     34/1,315 │ 肩膀水平度<0.1 且 身體前傾程度<0.2     │
│ 頭部位置正常          │  100.0% │  1,315/1,315 │ 頭部位置>-0.3                  │
│ 動作頻率正常          │    0.0% │      0/1,315 │ 右手動作頻率在10-60次/分鐘範圍內        │
│ 停頓時間正常          │  100.0% │  1,315/1,315 │ 右手停頓時間<5.0秒                │
└───────────────────┴─────────┴─────────────┴────────────────────────────┘


====================================================================================================
第三部分：數值解讀範例
====================================================================================================

以實際數據第一行為例：
151.82 | 130.45 | 162.35 | 128.63 | 0.500 | 0.480 | 0.402 | 2.009 | ...

詳細解讀：
┌─ 角度分析 ─────────────────────────────────────────────────────────────────┐
│ • 左手肘角度 151.82°：略微過度伸展（超出150°上限）❌                        │
│ • 右手肘角度 130.45°：在正常範圍內（90°-150°）✅                           │
│ • 左手腕角度 162.35°：檢測到明顯旋轉動作（>15°）✅                         │
│ • 右手腕角度 128.63°：檢測到旋轉動作（>15°）✅                             │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 動作分析 ─────────────────────────────────────────────────────────────────┐
│ • 左臂伸展程度 0.500：中等伸展程度                                         │
│ • 右臂伸展程度 0.480：中等伸展程度                                         │
│ • 左手腕速度 0.402：接近正常上限（0.5），動作稍快                          │
│ • 右手腕速度 2.009：明顯過快（超出0.5上限4倍）❌                           │
└────────────────────────────────────────────────────────────────────────────┘

┌─ 合規性分析 ───────────────────────────────────────────────────────────────┐
│ • 最終SOP評分：0.375 (37.5%)                                              │
│ • 評分等級：需改進（<40%）                                                 │
│ • 通過項目：3/8 項                                                        │
│ • 主要問題：手肘角度、動作速度、姿勢需要改進                               │
└────────────────────────────────────────────────────────────────────────────┘

====================================================================================================
第四部分：問題診斷與建議
====================================================================================================

🚨 發現的主要問題：
2. 動作速度問題：通過率僅 35.4%
3. 姿勢問題：通過率僅 2.6%

💡 改進建議：

1. 【閾值調整建議】
   • 速度閾值：考慮將上限從0.5調整為1.0-2.0
   • 肩膀水平度：考慮將標準從0.1放寬至0.15-0.2
   • 手肘角度：可考慮將範圍擴大為85°-155°

2. 【系統優化建議】
   • 增加資料平滑處理，減少檢測誤差
   • 建立個人化基準線，適應不同工作者
   • 加入時間窗口分析，避免瞬時異常影響整體評分

3. 【監控策略建議】
   • 重點關注持續性問題，而非瞬時異常
   • 結合影片回放驗證異常檢測的準確性
   • 建立趨勢分析，追蹤長期改進效果

4. 【實際應用建議】
   • 根據具體工作環境調整標準
   • 定期校準檢測系統
   • 建立工作者培訓回饋機制

====================================================================================================
第五部分：技術說明
====================================================================================================

📋 重要技術說明：

1. 【座標系統】
   • 所有座標都是標準化的（0-1範圍）
   • 不是真實物理尺寸，需要結合實際畫面理解

2. 【速度計算】
   • 單位：標準化座標/秒
   • 1.0 ≈ 每秒橫跨整個畫面
   • 實際物理速度需要結合攝影機參數計算

3. 【角度計算】
   • 使用3D向量夾角公式
   • 角度範圍：0°-180°
   • 計算基於MediaPipe檢測的關鍵點

4. 【資料品質】
   • 空白值：計算所需歷史資料不足
   • 0值：無檢測到該特徵或初始狀態
   • TRUE/FALSE：基於預設閾值的自動判斷

5. 【使用限制】
   • 閾值可能需要根據實際環境調整
   • 檢測精度受光照、角度、遮擋影響
   • 建議結合人工驗證使用

====================================================================================================
報告結束
====================================================================================================
生成時間：2025年07月10日 17:20:01
資料來源：sop_features_繁體中文.csv
報告版本：v1.0 統整版
====================================================================================================
