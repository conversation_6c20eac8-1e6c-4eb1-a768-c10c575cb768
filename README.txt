
# SOP 姿態監控與行為辨識系統（完整版）

## 系統功能：
- ✅ 使用 YOLOv8 偵測人與工具
- ✅ 使用 MediaPipe 擷取姿勢關鍵點
- ✅ 自動化擷取骨架資料（CSV）
- ✅ 智能時間轉換與資料中文化
- ✅ 深度SOP特徵提取與分析
- ✅ 自定義 LSTM 模型訓練與儲存
- ✅ 即時推論 SOP 步驟
- ✅ 可即時視覺化骨架與邊框顯示（GUI 模式）
- ✅ 完整的SOP合規性評分系統
- ✅ 詳細的分析報告生成

## 核心程式檔案：
- `pose_extractor.py`：從影片擷取姿態關鍵點資料並存為 CSV
- `lstm_trainer.py`：訓練LSTM模型，將姿態特徵轉為動作分類
- `inference.py`：使用訓練好的模型判斷影片中的 SOP 步驟
- `yolo_pose_gui.py`：整合 YOLO 與 MediaPipe 的即時監控 GUI
- `test_detection.py`：測試姿態檢測功能

## 資料處理工具（tools/ 資料夾）：
- `add_time_to_csv.py`：將幀數轉換為時間資訊
- `convert_csv_to_chinese.py`：將姿態資料欄位名稱轉換為繁體中文
- `csv_change.py`：從姿態資料中提取SOP監控特徵
- `convert_features_to_chinese.py`：將特徵欄位名稱轉換為繁體中文
- `快速執行.bat`：一鍵執行所有資料轉換工具
- `使用教學.txt`：詳細的工具使用教學

## 資料檔案：
- `pose_data_含時間.csv`：包含時間資訊的姿態資料
- `pose_data_繁體中文.csv`：繁體中文版姿態資料
- `sop_features_繁體中文.csv`：繁體中文版SOP特徵資料
- `SOP完整分析報告_統整版.txt`：詳細的特徵定義與分析報告

## 配置檔案：
- `task_area_config.json`：工作區域配置
- `yolov8n.pt`：YOLOv8 預訓練模型

## 快速開始：

### 1. 姿態資料提取
```bash
python pose_extractor.py
```

### 2. 資料處理（使用工具集）
```bash
# 方法1：使用批次檔案（推薦）
tools\快速執行.bat

# 方法2：手動執行
python tools\add_time_to_csv.py
python tools\convert_csv_to_chinese.py
python tools\csv_change.py
python tools\convert_features_to_chinese.py
```

### 3. 模型訓練
```bash
python lstm_trainer.py
```

### 4. 即時監控
```bash
python yolo_pose_gui.py
```

### 5. 推論分析
```bash
python inference.py
```

## 完整工作流程：

```
影片檔案 (1.mp4)
    ↓
[pose_extractor.py] → 原始姿態資料
    ↓
[tools/資料處理工具] → 處理後的特徵資料
    ↓
[lstm_trainer.py] → 訓練LSTM模型
    ↓
[yolo_pose_gui.py] → 即時SOP監控
    ↓
[inference.py] → SOP步驟推論
```

## 系統特色：

### 🎯 智能特徵提取
- 35種SOP監控特徵
- 角度、速度、距離、姿勢分析
- 軌跡規律性與穩定性檢測
- 工作節奏與頻率分析

### 📊 合規性評分
- 8項SOP檢查標準
- 即時合規性評分（0-1分）
- 詳細的問題診斷
- 改進建議提供

### 🔧 便利工具
- 一鍵資料轉換
- 繁體中文介面
- 詳細使用教學
- 批次處理功能

### 📈 分析報告
- 完整的特徵定義說明
- 實際數據統計分析
- 數值解讀範例
- 問題診斷與建議

## 技術需求：
- Python 3.7+
- OpenCV
- MediaPipe
- TensorFlow/Keras
- Pandas, NumPy
- Ultralytics (YOLOv8)

## 使用說明：
詳細的使用教學請參考：`tools/使用教學.txt`

## 注意事項：
1. 確保影片檔案放在專案根目錄
2. 首次使用建議先閱讀工具使用教學
3. 資料處理請按照建議順序執行
4. 模型訓練需要足夠的標記資料
