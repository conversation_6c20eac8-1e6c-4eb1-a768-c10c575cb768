# 影片1訓練數據資料夾

## 📁 資料夾說明
此資料夾包含基於 **影片1 (1.mp4)** 重新訓練產生的所有數據檔案。

## 🎯 訓練流程
1. **姿態提取**: 使用新版 `pose_extractor.py` 處理影片1
2. **數據轉換**: 轉換為標準訓練格式並添加時間和標籤
3. **特徵提取**: 使用 `tools/csv_change.py` 提取 SOP 特徵
4. **模型訓練**: 使用 `pytorch_lstm_trainer.py` 訓練模型

## 📊 數據統計
- **總幀數**: 1,195 幀
- **動作類別**: 18 種 SOP 動作 + 待機
- **特徵數量**: 35 個生物力學特徵
- **模型準確率**: 99.64%

## 📁 檔案結構

### 原始姿態數據
- `pose_output/1_pose_data.csv` - 原始姿態關鍵點數據
- `pose_output/1_pose_data.json` - JSON 格式姿態數據
- `pose_output/pose_extractor_*.log` - 處理日誌

### 處理後數據
- `pose_data.csv` - 標準格式姿態數據
- `pose_data_含時間.csv` - 添加時間資訊的數據
- `pose_data_含時間_已標記.csv` - 添加 SOP 動作標籤的數據

### 特徵數據
- `sop_features_已標記.csv` - 最終的 SOP 特徵數據（用於訓練）

## 🏷️ 動作標籤定義

### 螺絲鎖緊動作
- 鎖第1顆螺絲 (1-4秒)
- 鎖第2顆螺絲 (5-6秒)
- 鎖第3顆螺絲 (6-8秒)
- 鎖第4顆螺絲 (9-10秒)
- 鎖第5顆螺絲 (14-16秒)
- 鎖第6顆螺絲 (17-19秒)
- 鎖第7顆螺絲 (23-24秒)
- 鎖第8顆螺絲 (24-26秒)
- 鎖第9顆螺絲 (26-28秒)
- 鎖第10顆螺絲 (35-36秒)
- 鎖第11顆螺絲 (37-39秒)
- 鎖第12顆螺絲 (40-42秒)
- 鎖第13顆螺絲 (43-45秒)
- 鎖第14顆螺絲 (45-46秒)
- 鎖第15顆螺絲 (46-47秒)
- 鎖第16顆螺絲 (48-50秒)

### 其他動作
- 旋轉工件(順時針) (20-22秒, 31-32秒, 50-52秒)
- 結束動作 (52-53秒)
- 待機 (其他時間)

## 📈 特徵說明

### 角度特徵
- 左手肘角度、右手肘角度
- 左手腕角度、右手腕角度

### 運動特徵
- 左臂伸展程度、右臂伸展程度
- 左手腕速度、右手腕速度
- 左手軌跡規律性、右手軌跡規律性

### 姿勢特徵
- 雙手距離
- 肩膀水平度
- 身體前傾程度
- 頭部位置

### 精細動作特徵
- 左手手指張開度、右手手指張開度
- 左手腕穩定性、右手腕穩定性

### 時間特徵
- 左手動作頻率、右手動作頻率
- 左手停頓時間、右手停頓時間
- 工作節奏

### 合規性特徵
- 手肘角度正常
- 手腕旋轉正常
- 動作速度正常
- 軌跡規律正常
- 姿勢正常
- 頭部位置正常
- 動作頻率正常
- 停頓時間正常
- SOP合規性評分

## 🎯 使用說明

### 重新訓練模型
```bash
# 使用此資料夾的特徵數據訓練
python pytorch_lstm_trainer.py
```

### 查看數據
```python
import pandas as pd

# 查看特徵數據
df = pd.read_csv('video1_training_data/sop_features_已標記.csv')
print(df.head())
print(df['label'].value_counts())
```

## 📅 創建時間
2025-07-11 14:29

## 🔗 相關檔案
- 訓練模型: `../enhanced_sop_models/`
- 原始影片: `../1.mp4`
- 處理後影片: `../1_processed_new.mp4`
