import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import joblib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

# 設置繁體中文字體支援
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 設置設備
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用設備: {device}")

class SOPLSTM(nn.Module):
    """SOP LSTM模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes, dropout=0.2):
        super(SOPLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM層
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )
        
        # 全連接層
        self.fc1 = nn.Linear(hidden_size, 64)
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(64, num_classes)
        self.relu = nn.ReLU()
        
    def forward(self, x):
        # LSTM層
        lstm_out, _ = self.lstm(x)
        
        # 取最後一個時間步的輸出
        out = lstm_out[:, -1, :]
        
        # 全連接層
        out = self.fc1(out)
        out = self.relu(out)
        out = self.dropout(out)
        out = self.fc2(out)
        
        return out

def load_pytorch_model():
    """載入 PyTorch 模型和編碼器"""
    try:
        print("載入 PyTorch 模型...")
        
        # 載入編碼器
        label_encoder = joblib.load('pytorch_models/label_encoder_pytorch.pkl')
        scaler = joblib.load('pytorch_models/feature_scaler_pytorch.pkl')
        
        # 載入模型
        model_data = torch.load('pytorch_models/sop_pytorch_model.pth', map_location=device)
        model_config = model_data['model_config']
        
        # 創建模型實例
        model = SOPLSTM(
            input_size=model_config['input_size'],
            hidden_size=model_config['hidden_size'],
            num_layers=model_config['num_layers'],
            num_classes=model_config['num_classes']
        )
        
        # 載入權重
        model.load_state_dict(model_data['model_state_dict'])
        model.to(device)
        model.eval()
        
        print("✅ PyTorch 模型載入成功")
        print(f"模型架構: {model_config}")
        print(f"標籤數量: {len(label_encoder.classes_)}")
        
        return model, label_encoder, scaler
        
    except Exception as e:
        print(f"❌ 模型載入失敗: {e}")
        raise

def create_sequences(X, y, sequence_length=10):
    """創建序列數據"""
    sequences = []
    labels = []

    for i in range(len(X) - sequence_length + 1):
        sequence = X[i:i + sequence_length]
        sequences.append(sequence)
        # 使用序列最後一個時間步的標籤
        labels.append(y[i + sequence_length - 1])

    return np.array(sequences), labels

def test_model_on_video2():
    """測試模型在影片2上的表現"""
    print("=" * 60)
    print("測試 PyTorch 模型在影片2上的表現")
    print("=" * 60)
    
    try:
        # 載入模型
        model, label_encoder, scaler = load_pytorch_model()
        
        # 載入影片2的特徵數據
        print("載入影片2的特徵數據...")
        df = pd.read_csv('sop_features_已標記_繁體中文.csv', encoding='utf-8')
        print(f"數據形狀: {df.shape}")
        print(f"標籤分布:")
        print(df['標籤'].value_counts())

        # 檢查並過濾未知標籤
        known_labels = set(label_encoder.classes_)
        unknown_labels = set(df['標籤'].unique()) - known_labels

        if unknown_labels:
            print(f"\n⚠️ 發現未知標籤: {unknown_labels}")
            print("將過濾掉這些標籤的數據...")
            df_filtered = df[df['標籤'].isin(known_labels)]
            print(f"過濾後數據形狀: {df_filtered.shape}")
        else:
            df_filtered = df
            print("✅ 所有標籤都是已知的")

        # 準備特徵
        feature_columns = [col for col in df_filtered.columns if col not in ['幀數', '時間_秒', '標籤']]
        print(f"特徵數量: {len(feature_columns)}")

        # 提取特徵和標籤
        X = df_filtered[feature_columns].values
        y = df_filtered['標籤'].values
        
        # 標準化特徵
        X_scaled = scaler.transform(X)
        
        # 創建序列
        print("創建序列數據...")
        X_sequences, y_sequences = create_sequences(X_scaled, y, sequence_length=10)
        
        print(f"序列數據形狀: {X_sequences.shape}")
        print(f"序列標籤數量: {len(y_sequences)}")
        
        # 編碼標籤
        y_encoded = label_encoder.transform(y_sequences)
        
        # 轉換為 PyTorch tensors
        X_tensor = torch.FloatTensor(X_sequences).to(device)
        y_tensor = torch.LongTensor(y_encoded).to(device)
        
        # 進行預測
        print("進行預測...")
        predictions = []
        confidences = []
        
        with torch.no_grad():
            batch_size = 32
            for i in range(0, len(X_tensor), batch_size):
                batch_X = X_tensor[i:i+batch_size]
                outputs = model(batch_X)
                probabilities = torch.softmax(outputs, dim=1)
                predicted_classes = torch.argmax(outputs, dim=1)
                max_confidences = torch.max(probabilities, dim=1)[0]
                
                predictions.extend(predicted_classes.cpu().numpy())
                confidences.extend(max_confidences.cpu().numpy())
        
        # 解碼預測結果
        predicted_labels = label_encoder.inverse_transform(predictions)
        true_labels = y_sequences
        
        # 計算準確率
        accuracy = np.mean(np.array(predicted_labels) == np.array(true_labels))
        print(f"\n📊 測試結果:")
        print(f"總樣本數: {len(predictions)}")
        print(f"準確率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"平均信心度: {np.mean(confidences):.4f}")
        
        # 生成分類報告
        print("\n📋 詳細分類報告:")
        report = classification_report(true_labels, predicted_labels, 
                                     target_names=label_encoder.classes_, 
                                     zero_division=0)
        print(report)
        
        # 創建混淆矩陣
        print("生成混淆矩陣...")
        cm = confusion_matrix(true_labels, predicted_labels, labels=label_encoder.classes_)
        
        plt.figure(figsize=(20, 16))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=label_encoder.classes_, 
                   yticklabels=label_encoder.classes_,
                   cbar_kws={'label': '預測數量'})
        plt.title('影片2測試結果 - 混淆矩陣', fontsize=18, fontweight='bold', pad=20)
        plt.ylabel('真實標籤', fontsize=16, fontweight='bold')
        plt.xlabel('預測標籤', fontsize=16, fontweight='bold')
        plt.xticks(rotation=45, ha='right', fontsize=11)
        plt.yticks(rotation=0, fontsize=11)
        
        # 添加準確率信息
        plt.figtext(0.02, 0.02, f'總體準確率: {accuracy:.2%} ({len(predictions)} 個樣本)',
                   fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        
        plt.tight_layout()
        plt.savefig('pytorch_models/影片2測試結果_混淆矩陣.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 保存預測結果
        results_df = pd.DataFrame({
            '序列索引': range(len(predictions)),
            '真實標籤': true_labels,
            '預測標籤': predicted_labels,
            '信心度': confidences,
            '正確預測': np.array(predicted_labels) == np.array(true_labels)
        })
        
        results_df.to_csv('pytorch_models/影片2測試結果.csv', index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 測試完成！")
        print(f"📁 混淆矩陣已保存: pytorch_models/影片2測試結果_混淆矩陣.png")
        print(f"📁 詳細結果已保存: pytorch_models/影片2測試結果.csv")
        print("=" * 60)
        
        return accuracy, predicted_labels, true_labels, confidences
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_model_on_video2()
