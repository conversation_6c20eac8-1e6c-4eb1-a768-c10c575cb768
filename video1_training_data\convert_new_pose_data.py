import pandas as pd
import numpy as np
import os

def convert_new_pose_data():
    """將新的姿態數據轉換為訓練流程需要的格式"""
    
    print("🔄 開始轉換新的姿態數據...")
    
    # 讀取新的姿態數據
    input_file = 'pose_output/1_pose_data.csv'
    if not os.path.exists(input_file):
        print(f"❌ 找不到檔案: {input_file}")
        return
    
    df = pd.read_csv(input_file)
    print(f"📊 讀取數據: {df.shape[0]} 幀")
    
    # 創建新的 DataFrame，格式與原始 pose_data.csv 相同
    new_data = []
    
    for _, row in df.iterrows():
        frame_data = {
            'frame': int(row['frame_idx']) + 1,  # 從1開始計數
            'label': ''  # 空標籤，稍後添加
        }
        
        # 添加33個關鍵點的 x, y, z, visibility
        for i in range(33):
            x_col = f'landmark_{i}_x'
            y_col = f'landmark_{i}_y'
            z_col = f'landmark_{i}_z'
            vis_col = f'landmark_{i}_visibility'
            
            if x_col in row and not pd.isna(row[x_col]):
                # 將絕對座標轉換為相對座標 (0-1)
                # 假設影片尺寸為 1920x1080
                frame_data[f'{i}_x'] = row[x_col] / 1920.0
                frame_data[f'{i}_y'] = row[y_col] / 1080.0
                frame_data[f'{i}_z'] = row[z_col]
                frame_data[f'{i}_visibility'] = row[vis_col]
            else:
                # 如果數據缺失，設為0
                frame_data[f'{i}_x'] = 0.0
                frame_data[f'{i}_y'] = 0.0
                frame_data[f'{i}_z'] = 0.0
                frame_data[f'{i}_visibility'] = 0.0
        
        new_data.append(frame_data)
    
    # 創建新的 DataFrame
    new_df = pd.DataFrame(new_data)
    
    # 保存為 pose_data.csv
    output_file = 'pose_data.csv'
    new_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✅ 轉換完成，已保存為: {output_file}")
    print(f"📊 輸出數據: {new_df.shape[0]} 幀, {new_df.shape[1]} 欄位")
    
    # 顯示前幾行
    print("\n📋 前5行數據預覽:")
    print(new_df.head())
    
    return new_df

def add_time_info():
    """添加時間資訊"""
    print("\n🕐 添加時間資訊...")
    
    # 讀取 pose_data.csv
    df = pd.read_csv('pose_data.csv')
    
    # 假設影片幀率為 29 FPS (從之前的日誌可以看到)
    fps = 29.0
    
    # 計算時間
    df['時間_秒'] = (df['frame'] - 1) / fps  # 從0秒開始
    df['時間_分秒'] = df['時間_秒'].apply(lambda x: f"{int(x//60):02d}:{x%60:05.2f}")
    
    # 重新排列欄位順序
    cols = ['frame', '時間_秒', '時間_分秒', 'label'] + [col for col in df.columns if col not in ['frame', '時間_秒', '時間_分秒', 'label']]
    df = df[cols]
    
    # 保存
    output_file = 'pose_data_含時間.csv'
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✅ 時間資訊已添加，保存為: {output_file}")
    
    return df

def add_labels():
    """添加 SOP 動作標籤"""
    print("\n🏷️ 添加 SOP 動作標籤...")
    
    # 定義時間標籤對應表 (基於影片1的實際內容)
    time_labels = [
        (1, 4, "鎖第1顆螺絲"),
        (5, 6, "鎖第2顆螺絲"),
        (6, 8, "鎖第3顆螺絲"),
        (9, 10, "鎖第4顆螺絲"),
        (14, 16, "鎖第5顆螺絲"),
        (17, 19, "鎖第6顆螺絲"),
        (20, 22, "旋轉工件(順時針)"),
        (23, 24, "鎖第7顆螺絲"),
        (24, 26, "鎖第8顆螺絲"),
        (26, 28, "鎖第9顆螺絲"),
        (31, 32, "旋轉工件(順時針)"),
        (35, 36, "鎖第10顆螺絲"),
        (37, 39, "鎖第11顆螺絲"),
        (40, 42, "鎖第12顆螺絲"),
        (43, 45, "鎖第13顆螺絲"),
        (45, 46, "鎖第14顆螺絲"),
        (46, 47, "鎖第15顆螺絲"),
        (48, 50, "鎖第16顆螺絲"),
        (50, 52, "旋轉工件(順時針)"),
        (52, 53, "結束動作")
    ]
    
    # 讀取含時間的數據
    df = pd.read_csv('pose_data_含時間.csv')
    
    # 初始化標籤為 "待機"
    df['label'] = "待機"
    
    # 根據時間範圍添加標籤
    labeled_count = 0
    for start_time, end_time, label in time_labels:
        mask = (df['時間_秒'] >= start_time) & (df['時間_秒'] <= end_time)
        count = mask.sum()
        if count > 0:
            df.loc[mask, 'label'] = label
            labeled_count += count
            print(f"  {label}: {count} 幀 ({start_time}-{end_time}秒)")
    
    # 保存
    output_file = 'pose_data_含時間_已標記.csv'
    df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✅ 標籤已添加，保存為: {output_file}")
    print(f"📊 已標記: {labeled_count} 幀")
    
    # 顯示標籤分布
    print("\n📋 標籤分布:")
    label_counts = df['label'].value_counts()
    for label, count in label_counts.items():
        print(f"  {label}: {count} 幀")
    
    return df

def main():
    """主函數"""
    print("=" * 60)
    print("🔄 轉換新的姿態數據為訓練格式")
    print("=" * 60)
    
    try:
        # 步驟1: 轉換格式
        convert_new_pose_data()
        
        # 步驟2: 添加時間資訊
        add_time_info()
        
        # 步驟3: 添加標籤
        add_labels()
        
        print("\n" + "=" * 60)
        print("✅ 數據轉換完成！")
        print("📁 生成的檔案:")
        print("  - pose_data.csv")
        print("  - pose_data_含時間.csv") 
        print("  - pose_data_含時間_已標記.csv")
        print("\n🎯 接下來可以運行:")
        print("  python tools/csv_change.py")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 轉換過程發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
