import pandas as pd

def add_labels_to_csv():
    """根據時間範圍為CSV檔案添加標籤"""
    
    # 定義時間標籤對應表
    time_labels = [
        (1, 4, "鎖第1顆螺絲"),
        (5, 6, "鎖第2顆螺絲"),
        (6, 8, "鎖第3顆螺絲"),
        (9, 10, "鎖第4顆螺絲"),
        (14, 16, "鎖第5顆螺絲"),
        (17, 19, "鎖第6顆螺絲"),
        (20, 22, "旋轉工件(順時針)"),
        (23, 24, "鎖第7顆螺絲"),
        (24, 26, "鎖第8顆螺絲"),
        (26, 28, "鎖第9顆螺絲"),
        (31, 32, "旋轉工件(順時針)"),
        (35, 36, "鎖第10顆螺絲"),
        (37, 39, "鎖第11顆螺絲"),
        (40, 42, "鎖第12顆螺絲"),
        (43, 45, "鎖第13顆螺絲"),
        (45, 46, "鎖第14顆螺絲"),
        (46, 47, "鎖第15顆螺絲"),
        (48, 50, "鎖第16顆螺絲"),
        (50, 52, "旋轉工件(順時針)"),
        (52, 53, "結束動作")
    ]
    
    try:
        # 讀取CSV檔案
        print("正在讀取 pose_data_含時間.csv...")
        df = pd.read_csv('pose_data_含時間.csv', encoding='utf-8-sig')
        
        # 檢查必要的欄位
        if '時間_秒' not in df.columns:
            print("錯誤：找不到 '時間_秒' 欄位")
            return
        
        # 初始化標籤欄位為空字串
        df['標籤'] = ""
        
        # 根據時間範圍添加標籤
        print("正在根據時間範圍添加標籤...")
        
        labeled_count = 0
        for start_time, end_time, label in time_labels:
            # 找到在時間範圍內的行
            mask = (df['時間_秒'] >= start_time) & (df['時間_秒'] <= end_time)
            count = mask.sum()
            
            if count > 0:
                df.loc[mask, '標籤'] = label
                labeled_count += count
                print(f"  {start_time:02d}-{end_time:02d}秒: {label} ({count} 行)")
            else:
                print(f"  警告：{start_time:02d}-{end_time:02d}秒 沒有找到對應的資料")
        
        # 統計未標記的資料
        unlabeled_count = (df['標籤'] == "").sum()
        
        # 將未標記的時間段標記為"待機"
        if unlabeled_count > 0:
            df.loc[df['標籤'] == "", '標籤'] = "待機"
            print(f"  未標記的時間段已標記為 '待機' ({unlabeled_count} 行)")
        
        # 保存修改後的檔案
        output_file = 'pose_data_含時間_已標記.csv'
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n標記完成！")
        print(f"總資料行數：{len(df):,}")
        print(f"已標記行數：{labeled_count:,}")
        print(f"待機行數：{unlabeled_count:,}")
        print(f"新檔案已保存為：{output_file}")
        
        # 顯示標籤統計
        print(f"\n標籤統計：")
        label_counts = df['標籤'].value_counts()
        for label, count in label_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {label}: {count:,} 行 ({percentage:.1f}%)")
        
        # 顯示時間範圍檢查
        print(f"\n時間範圍檢查：")
        print(f"  資料時間範圍：{df['時間_秒'].min():.2f} - {df['時間_秒'].max():.2f} 秒")
        print(f"  標記時間範圍：1 - 53 秒")
        
        # 檢查是否有時間重疊
        print(f"\n檢查時間重疊...")
        overlaps = []
        for i, (start1, end1, label1) in enumerate(time_labels):
            for j, (start2, end2, label2) in enumerate(time_labels[i+1:], i+1):
                if start2 < end1:  # 有重疊
                    overlaps.append(f"  {label1} ({start1}-{end1}秒) 與 {label2} ({start2}-{end2}秒)")
        
        if overlaps:
            print("發現時間重疊：")
            for overlap in overlaps:
                print(overlap)
        else:
            print("沒有發現時間重疊")
        
        # 顯示前幾行範例
        print(f"\n前10行標記範例：")
        sample_df = df[['幀數', '時間_秒', '時間_分秒', '標籤']].head(10)
        for _, row in sample_df.iterrows():
            print(f"  幀{row['幀數']:4d} | {row['時間_分秒']} | {row['標籤']}")
        
    except FileNotFoundError:
        print("錯誤：找不到 'pose_data_含時間.csv' 檔案")
        print("請確認檔案是否存在於當前目錄中")
    except Exception as e:
        print(f"處理過程中發生錯誤：{e}")

if __name__ == "__main__":
    add_labels_to_csv()
