@echo off
chcp 65001 >nul
echo ================================================================================
echo SOP姿態分析工具 - 快速執行腳本
echo ================================================================================
echo.

:menu
echo 請選擇要執行的工具：
echo.
echo 1. 添加時間資訊 (add_time_to_csv.py)
echo 2. 姿態資料中文化 (convert_csv_to_chinese.py)
echo 3. 提取SOP特徵 (csv_change.py)
echo 4. 特徵資料中文化 (convert_features_to_chinese.py)
echo 5. 完整處理流程 (執行所有步驟)
echo 6. 查看使用教學
echo 0. 退出
echo.
set /p choice=請輸入選項 (0-6): 

if "%choice%"=="1" goto time_convert
if "%choice%"=="2" goto pose_chinese
if "%choice%"=="3" goto extract_features
if "%choice%"=="4" goto features_chinese
if "%choice%"=="5" goto full_process
if "%choice%"=="6" goto show_help
if "%choice%"=="0" goto exit
echo 無效選項，請重新選擇
goto menu

:time_convert
echo.
echo ================================================================================
echo 執行：添加時間資訊
echo ================================================================================
cd /d "%~dp0.."
python tools\add_time_to_csv.py
echo.
pause
goto menu

:pose_chinese
echo.
echo ================================================================================
echo 執行：姿態資料中文化
echo ================================================================================
cd /d "%~dp0.."
python tools\convert_csv_to_chinese.py
echo.
pause
goto menu

:extract_features
echo.
echo ================================================================================
echo 執行：提取SOP特徵
echo ================================================================================
cd /d "%~dp0.."
python tools\csv_change.py
echo.
pause
goto menu

:features_chinese
echo.
echo ================================================================================
echo 執行：特徵資料中文化
echo ================================================================================
cd /d "%~dp0.."
python tools\convert_features_to_chinese.py
echo.
pause
goto menu

:full_process
echo.
echo ================================================================================
echo 執行：完整處理流程
echo ================================================================================
echo 將依序執行所有轉換步驟...
echo.
cd /d "%~dp0.."

echo 步驟1/4: 添加時間資訊...
python tools\add_time_to_csv.py
if errorlevel 1 (
    echo 錯誤：添加時間資訊失敗
    pause
    goto menu
)

echo.
echo 步驟2/4: 姿態資料中文化...
python tools\convert_csv_to_chinese.py
if errorlevel 1 (
    echo 錯誤：姿態資料中文化失敗
    pause
    goto menu
)

echo.
echo 步驟3/4: 提取SOP特徵...
python tools\csv_change.py
if errorlevel 1 (
    echo 錯誤：提取SOP特徵失敗
    pause
    goto menu
)

echo.
echo 步驟4/4: 特徵資料中文化...
python tools\convert_features_to_chinese.py
if errorlevel 1 (
    echo 錯誤：特徵資料中文化失敗
    pause
    goto menu
)

echo.
echo ================================================================================
echo 完整處理流程執行完成！
echo ================================================================================
echo 生成的檔案：
echo • pose_data_含時間.csv
echo • pose_data_繁體中文.csv
echo • sop_features.csv
echo • sop_features_繁體中文.csv
echo ================================================================================
pause
goto menu

:show_help
echo.
echo ================================================================================
echo 使用教學
echo ================================================================================
type tools\使用教學.txt
echo.
pause
goto menu

:exit
echo.
echo 感謝使用SOP姿態分析工具！
echo.
pause
exit
